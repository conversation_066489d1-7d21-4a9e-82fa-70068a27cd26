<!-- Header Toolbar -->
<mat-toolbar color="primary" class="dashboard-header">
  <span class="header-title">
    <mat-icon>business</mat-icon>
    Company Admin Dashboard
  </span>
  <span class="spacer"></span>
  <button mat-icon-button (click)="loadDashboardData()" matTooltip="Refresh">
    <mat-icon>refresh</mat-icon>
  </button>
  <button mat-button (click)="logout()">
    <mat-icon>logout</mat-icon>
    Logout
  </button>
</mat-toolbar>

<!-- Loading Spinner -->
<div *ngIf="isLoading" class="loading-container">
  <mat-spinner></mat-spinner>
  <p>Loading dashboard...</p>
</div>

<!-- Main Dashboard Content -->
<div *ngIf="!isLoading" class="dashboard-container">

  <!-- Welcome Section -->
  <mat-card class="welcome-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>waving_hand</mat-icon>
        Welcome back, {{ dashboardStats?.admin_name }}!
      </mat-card-title>
      <mat-card-subtitle>
        {{ dashboardStats?.company_name }} • {{ currentDate | date:'fullDate' }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>Here's your company's content management overview for today.</p>
    </mat-card-content>
  </mat-card>

  <!-- Project Summary Panel -->
  <div class="stats-grid">
    <mat-card class="stat-card projects-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>folder</mat-icon>
          Projects Overview
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalProjects() }}</span>
          <span class="stat-label">Total Projects</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveProjects() }}</span>
          <span class="stat-label">Active Projects</span>
        </div>
        <div class="stat-item">
          <span class="stat-number completed">{{ getCompletedProjects() }}</span>
          <span class="stat-label">Completed Projects</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>article</mat-icon>
          Content Statistics
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalPosts() }}</span>
          <span class="stat-label">Total Posts</span>
        </div>
        <div class="stat-item">
          <span class="stat-number pending" [matBadge]="getPendingReviews()"
                matBadgeColor="warn" [matBadgeHidden]="getPendingReviews() === 0">
            {{ getPendingReviews() }}
          </span>
          <span class="stat-label">Pending Reviews</span>
        </div>
        <div class="stat-item">
          <span class="stat-number approved">{{ getApprovedPosts() }}</span>
          <span class="stat-label">Approved</span>
        </div>
        <div class="stat-item">
          <span class="stat-number rejected">{{ getRejectedPosts() }}</span>
          <span class="stat-label">Rejected</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card creators-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>people</mat-icon>
          Content Creators
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalCreators() }}</span>
          <span class="stat-label">Total Creators</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveCreatorsCount() }}</span>
          <span class="stat-label">Active Creators</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Content Review Activity -->
  <div class="content-section">
    <!-- Pending Reviews -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>pending_actions</mat-icon>
          Pending Content Reviews
          <mat-chip-set *ngIf="pendingReviews.length > 0">
            <mat-chip color="warn">{{ pendingReviews.length }}</mat-chip>
          </mat-chip-set>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="pendingReviews.length === 0" class="empty-state">
          <mat-icon>check_circle</mat-icon>
          <p>No pending reviews! All caught up.</p>
        </div>

        <div *ngIf="pendingReviews.length > 0" class="content-list">
          <div *ngFor="let post of pendingReviews" class="content-item">
            <div class="content-info">
              <div class="content-header">
                <h4>{{ post.title }}</h4>
                <mat-chip [style.background-color]="getStatusColor(post.status)">
                  <mat-icon>{{ getStatusIcon(post.status) }}</mat-icon>
                  {{ post.status | titlecase }}
                </mat-chip>
              </div>
              <p class="content-description">{{ post.description }}</p>
              <div class="content-meta">
                <span><mat-icon>person</mat-icon> Creator: {{ post.creator_name || 'Unknown' }}</span>
                <span><mat-icon>folder</mat-icon> Project: {{ post.project_name || 'Unknown' }}</span>
                <span><mat-icon>schedule</mat-icon> {{ formatDate(post.scheduled_time) }}</span>
              </div>
            </div>

            <!-- Media Preview -->
            <div *ngIf="post.media_url" class="media-preview">
              <img *ngIf="post.media_url.includes('image')" [src]="post.media_url" alt="Content preview">
              <video *ngIf="post.media_url.includes('video')" [src]="post.media_url" controls></video>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
              <button mat-raised-button color="primary" (click)="approveContent(post.id)">
                <mat-icon>check</mat-icon>
                Approve
              </button>
              <button mat-raised-button color="accent" (click)="requestChanges(post.id)">
                <mat-icon>edit</mat-icon>
                Request Changes
              </button>
              <button mat-raised-button color="warn" (click)="rejectContent(post.id)">
                <mat-icon>close</mat-icon>
                Reject
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Recent Content Activity -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Recent Content Activity
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="recentContent.length === 0" class="empty-state">
          <mat-icon>article</mat-icon>
          <p>No recent content activity.</p>
        </div>

        <mat-list *ngIf="recentContent.length > 0">
          <mat-list-item *ngFor="let post of recentContent">
            <mat-icon matListItemIcon [style.color]="getStatusColor(post.status)">
              {{ getStatusIcon(post.status) }}
            </mat-icon>
            <div matListItemTitle>{{ post.title }}</div>
            <div matListItemLine>
              {{ post.creator_name || 'Unknown Creator' }} • {{ formatDate(post.scheduled_time) }}
            </div>
            <mat-chip matListItemMeta [style.background-color]="getStatusColor(post.status)">
              {{ post.status | titlecase }}
            </mat-chip>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Content Creator Overview -->
  <mat-card class="creators-overview-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>people</mat-icon>
        Content Creator Overview
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="creators.length === 0" class="empty-state">
        <mat-icon>person_add</mat-icon>
        <p>No content creators assigned yet.</p>
      </div>

      <div *ngIf="creators.length > 0" class="creators-table-container">
        <table mat-table [dataSource]="creators" class="creators-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Creator</th>
            <td mat-cell *matCellDef="let creator">
              <div class="creator-info">
                <div class="creator-name">{{ creator.full_name }}</div>
                <div class="creator-email">{{ creator.email }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Projects Column -->
          <ng-container matColumnDef="projects">
            <th mat-header-cell *matHeaderCellDef>Assigned Projects</th>
            <td mat-cell *matCellDef="let creator">
              <mat-chip-set>
                <mat-chip *ngFor="let project of creator.projects">
                  {{ project.name }}
                </mat-chip>
              </mat-chip-set>
            </td>
          </ng-container>

          <!-- Total Posts Column -->
          <ng-container matColumnDef="total_posts">
            <th mat-header-cell *matHeaderCellDef>Total Posts</th>
            <td mat-cell *matCellDef="let creator">
              <span class="post-count">{{ creator.total_posts }}</span>
            </td>
          </ng-container>

          <!-- Pending Posts Column -->
          <ng-container matColumnDef="pending_posts">
            <th mat-header-cell *matHeaderCellDef>Pending</th>
            <td mat-cell *matCellDef="let creator">
              <span class="pending-count" [matBadge]="creator.pending_posts"
                    matBadgeColor="warn" [matBadgeHidden]="creator.pending_posts === 0">
                {{ creator.pending_posts }}
              </span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let creator">
              <mat-chip [color]="creator.status === 'active' ? 'primary' : 'basic'">
                <mat-icon>{{ creator.status === 'active' ? 'check_circle' : 'pause_circle' }}</mat-icon>
                {{ creator.status | titlecase }}
              </mat-chip>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="creatorsColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: creatorsColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Action Shortcuts -->
  <mat-card class="action-shortcuts-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>flash_on</mat-icon>
        Quick Actions
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="action-grid">
        <button mat-raised-button color="primary" (click)="loadDashboardData()">
          <mat-icon>refresh</mat-icon>
          Refresh Dashboard
        </button>
        <button mat-raised-button color="accent"
                [disabled]="getPendingReviews() === 0">
          <mat-icon>rate_review</mat-icon>
          Review All Pending ({{ getPendingReviews() }})
        </button>
        <button mat-raised-button>
          <mat-icon>analytics</mat-icon>
          View Analytics
        </button>
        <button mat-raised-button>
          <mat-icon>settings</mat-icon>
          Settings
        </button>
      </div>
    </mat-card-content>
  </mat-card>

</div>
