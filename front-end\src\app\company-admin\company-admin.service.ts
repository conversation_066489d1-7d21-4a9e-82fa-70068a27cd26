import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DashboardStats, ContentPost, Creator, ActionResponse } from './models/dashboard.models';

@Injectable({
  providedIn: 'root'
})
export class CompanyAdminService {
  private baseUrl = 'http://127.0.0.1:8000/api/company-admin';

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Dashboard Statistics
  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.baseUrl}/dashboard_stats/`, {
      headers: this.getHeaders()
    });
  }

  // Recent Content
  getRecentContent(): Observable<ContentPost[]> {
    return this.http.get<ContentPost[]>(`${this.baseUrl}/recent_content/`, {
      headers: this.getHeaders()
    });
  }

  // Pending Reviews
  getPendingReviews(): Observable<ContentPost[]> {
    return this.http.get<ContentPost[]>(`${this.baseUrl}/pending_reviews/`, {
      headers: this.getHeaders()
    });
  }

  // Creators
  getCreators(): Observable<Creator[]> {
    return this.http.get<Creator[]>(`${this.baseUrl}/creators/`, {
      headers: this.getHeaders()
    });
  }

  // Content Actions
  approveContent(postId: number): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.baseUrl}/${postId}/approve_content/`, {}, {
      headers: this.getHeaders()
    });
  }

  rejectContent(postId: number): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.baseUrl}/${postId}/reject_content/`, {}, {
      headers: this.getHeaders()
    });
  }

  requestChanges(postId: number): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.baseUrl}/${postId}/request_changes/`, {}, {
      headers: this.getHeaders()
    });
  }

  // Utility methods
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800';
      case 'posted': return '#4caf50';
      case 'rejected': return '#f44336';
      case 'rework': return '#ff5722';
      case 'draft': return '#9e9e9e';
      case 'scheduled': return '#2196f3';
      default: return '#9e9e9e';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'submitted': return 'schedule';
      case 'posted': return 'check_circle';
      case 'rejected': return 'cancel';
      case 'rework': return 'edit';
      case 'draft': return 'draft';
      case 'scheduled': return 'event';
      default: return 'help';
    }
  }
}
