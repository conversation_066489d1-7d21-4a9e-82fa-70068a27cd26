import { Component, OnInit } from '@angular/core';
import { Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { PostService } from '../../services/post.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';



@Component({
  selector: 'app-create-post',
  templateUrl: './create-post.component.html',
  styleUrls: ['./create-post.component.css'],
  imports:[CommonModule, ReactiveFormsModule],
})
export class CreatePostComponent implements OnInit {
  postForm!: FormGroup;
  selectedImages: File[] = [];
  selectedVideos: File[] = [];

  constructor(
    private fb: FormBuilder,
    private postService: PostService,
    private router: Router
  ) {}

  ngOnInit(): void {
  this.postForm = this.fb.group({
    title: ['', Validators.required],
    description: ['', Validators.required],        // rename content → description
    scheduled_date: ['', Validators.required]      // rename scheduledDate → scheduled_date
  });
}


  onImageSelected(event: any): void {
    this.selectedImages = Array.from(event.target.files);
  }

  onVideoSelected(event: any): void {
    this.selectedVideos = Array.from(event.target.files);
  }

  onSubmit(): void {
  if (this.postForm.invalid) {
    this.postForm.markAllAsTouched();
    return;
  }

  const formData = new FormData();
  formData.append('title', this.postForm.get('title')?.value);
  formData.append('description', this.postForm.get('description')?.value);
  formData.append('scheduled_date', this.postForm.get('scheduled_date')?.value);

  // For now, just upload the first image or video as media (adjust backend for multiple)
  if (this.selectedImages.length > 0) {
    formData.append('media', this.selectedImages[0], this.selectedImages[0].name);
  } else if (this.selectedVideos.length > 0) {
    formData.append('media', this.selectedVideos[0], this.selectedVideos[0].name);
  }

  this.postService.createPostWithFiles(formData).subscribe({
    next: () => this.router.navigate(['/creators/dashboard/post-list']),
    error: () => alert('Failed to create post. Please try again.')
  });
}

}
