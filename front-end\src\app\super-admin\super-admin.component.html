<div class="container">
  <h2>Super Admin Dashboard</h2>

  <!-- Create Company -->
  <section class="section">
    <h3>Add Company</h3>
    <input [(ngModel)]="newCompanyName" placeholder="Enter company name" />
    <button (click)="createCompany()" [disabled]="!newCompanyName.trim()">Create Company</button>
  </section>

  <hr />

  <!-- List of Companies and Manage Projects -->
  <section *ngFor="let company of companies" class="company-block">
    <h3>{{ company.name }}</h3>
    <button (click)="selectedCompanyId = company.id; loadProjects(company.id)">
      Manage
    </button>

    <div *ngIf="selectedCompanyId === company.id" class="projects-section">

      <!-- Add Project -->
      <div class="add-project-form">
        <h4>Add Project to {{ company.name }}</h4>
        <input [(ngModel)]="newProjectName" placeholder="Enter project name" />
        <button (click)="addProjectToCompany()" [disabled]="!newProjectName.trim()">
          Add Project
        </button>
      </div>

      <!-- List Projects -->
      <h4>Projects</h4>
      <ul *ngIf="projects && projects[company.id] && projects[company.id].length > 0; else noProjects">

        <li *ngFor="let project of projects[company.id]" class="project-item">
          <strong>{{ project.name }}</strong>

          <!-- Assigned Creators -->
          <div class="assigned-creators" *ngIf="project.creators_detail?.length > 0; else noCreators">
            <p>Assigned Creators:</p>
            <ul>
              <li *ngFor="let creator of project.creators_detail">
                {{ creator.name }}
              </li>
            </ul>
          </div>

          <ng-template #noCreators>
            <p>No creators assigned yet.</p>
          </ng-template>

          <!-- Assign Creator -->
          <div class="assign-creator-form">
            <select [(ngModel)]="creatorSelections[project.id]">
              <option [ngValue]="null">-- Select Creator --</option>
              <option *ngFor="let creator of creators" [ngValue]="creator.id">
                {{ creator.name || creator.username }}
              </option>
              
            </select>
            <button (click)="assignCreatorToProject(project.id, creatorSelections[project.id])">
              Assign Creator
            </button>
          </div>
        </li>
      </ul>

      <ng-template #noProjects>
        <p>No projects added yet.</p>
      </ng-template>

    </div>
  </section>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading">Loading...</div>

  <!-- Error Message -->
  <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>

  <!-- Logout -->
  <button (click)="logout()">Logout</button>