import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';     
import { FormsModule } from '@angular/forms'; 
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon'; // ✅ this one fixes your error
import { MatSnackBarModule } from '@angular/material/snack-bar'; // for notifications

@Component({
  selector: 'app-super-admin',
  standalone: true,
  imports: [CommonModule,
  FormsModule,
  MatSidenavModule,
  MatToolbarModule,
  MatButtonModule,
  MatFormFieldModule,
  MatInputModule,
  MatCardModule,
  MatSelectModule,
  MatExpansionModule,
  MatProgressSpinnerModule,
  MatListModule,
  MatIconModule],
  templateUrl: './super-admin.component.html',
  styleUrls: ['./super-admin.component.css']
})
export class SuperAdminComponent implements OnInit {
  companies: any[] = [];
  creators: any[] = [];
  creatorSelections: { [key: number]: number | null } = {};  // Keeps selected creator per project

  newCompanyName = '';
  selectedCompanyId: number | null = null;
  newProjectName = '';
  newCreatorId: number | null = null;

  isLoading = false;
  errorMessage: string | null = null; // Track error messages
  projects: { [key: number]: any[] } = {};  // Store projects by company ID

  constructor(private http: HttpClient, private router: Router) {}

  ngOnInit() {
    this.loadCompanies();
    this.loadCreators();
  }

  loadCompanies() {
    this.isLoading = true;
    this.errorMessage = null;
    const token = localStorage.getItem('access_token');
    const headers = { 'Authorization': `Bearer ${token}` };
    this.http.get<any[]>('http://127.0.0.1:8000/api/super-admin/list_companies/',{headers})
      .subscribe(
        data => {
          this.companies = data;
          this.companies.forEach(company => {
            this.loadProjects(company.id);  // Load projects for each company
          });
          this.isLoading = false;
        },
        error => {
          this.isLoading = false;
          this.errorMessage = 'Failed to load companies. Please try again later.';
        }
      );
  }

  createCompany() {
    const newCompany = { name: this.newCompanyName };
    const token = localStorage.getItem('access_token');

    const headers = {
      'Authorization': `Bearer ${token}`
    };
    this.http.post('http://127.0.0.1:8000/api/super-admin/add_company/', newCompany, {headers})
      .subscribe(() => {
        alert('Company created successfully!');
        this.newCompanyName = '';
        this.loadCompanies();
      });
  }

  addProjectToCompany() {
    if (this.selectedCompanyId !== null) {
      const projectData = { name: this.newProjectName };
      const token = localStorage.getItem('access_token');
      const headers = { 'Authorization': `Bearer ${token}` };
      this.http.post(`http://127.0.0.1:8000/api/super-admin/${this.selectedCompanyId}/add_project/`, projectData, {headers})
        .subscribe(
          () => {
            alert('Project added successfully!');
            this.newProjectName = '';
            if (this.selectedCompanyId !== null) {
              this.loadProjects(this.selectedCompanyId);  // Reload projects for the selected company
            }
          },
          (error) => {
            console.error('Error adding project:', error);
            alert('Failed to add project. Please try again later.');
          }
        );
    }
  }

  loadProjects(companyId: number) {
    this.isLoading = true;
    const token = localStorage.getItem('access_token');
    const headers = { 'Authorization': `Bearer ${token}` };
    this.http.get<any[]>(`http://127.0.0.1:8000/api/super-admin/list_projects/${companyId}/`, {headers})
      .subscribe(
        data => {
          this.projects[companyId] = data;
          this.isLoading = false;
        },
        error => {
          this.isLoading = false;
          this.errorMessage = 'Failed to load projects. Please try again later.';
        }
      );
  }

  loadCreators() {
    this.isLoading = true;
    const token = localStorage.getItem('access_token');
    const headers = { 'Authorization': `Bearer ${token}` };
    this.http.get<any[]>('http://127.0.0.1:8000/api/users/?role=creator', {headers})
      .subscribe(
        data => {
          this.creators = data;
          this.isLoading = false;
        },
        error => {
          this.isLoading = false;
          this.errorMessage = 'Failed to load creators. Please try again later.';
        }
      );
  }

  assignCreatorToProject(projectId: number, creatorId: number | null) {
    if (!creatorId) {
      alert("Please select a creator first.");
      return;
    }

    const creatorData = { creator_id: creatorId };
    const token = localStorage.getItem('access_token');
    const headers = { 'Authorization': `Bearer ${token}` };
    this.http.post(`http://127.0.0.1:8000/api/super-admin/${projectId}/assign_creator/`, creatorData, {headers})

      .subscribe(
        () => {
          alert('Creator assigned successfully!');
          this.creatorSelections[projectId] = null;
        },
        error => {
          console.error('Error assigning creator:', error);
          alert('Failed to assign creator. Please try again later.');
        }
      );
  }

  logout() {
    localStorage.clear();
    this.router.navigate(['/login']);
  }
}

