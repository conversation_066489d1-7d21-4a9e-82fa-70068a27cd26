import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FullCalendarWrapperModule } from '../../../shared/full-calendar-wrapper.module'; 
import dayGridPlugin from '@fullcalendar/daygrid';
import { PostService } from '../../services/post.service';
import { Post } from '../../models/post.model';

@Component({
  selector: 'app-calendar-view',
  standalone: true,
  imports: [CommonModule, FullCalendarWrapperModule],
  templateUrl: './calendar-view.component.html',
  styleUrls: ['./calendar-view.component.css']
})
export class CalendarViewComponent implements OnInit {
  calendarPlugins = [dayGridPlugin];
  calendarEvents = [{ title: 'Event 1', date: '2025-05-16' }];

  constructor(private postService: PostService) {}

  ngOnInit(): void {
    this.postService.getPosts().subscribe((posts: Post[]) => {
      this.calendarEvents = posts.map(post => ({
        title: post.title,
        date: post.scheduledDate
      }));
    });
  }
}
