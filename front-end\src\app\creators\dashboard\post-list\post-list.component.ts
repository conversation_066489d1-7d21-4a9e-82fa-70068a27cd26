import { Component, OnInit } from '@angular/core';
import { Post } from '../../models/post.model';
import { PostService } from '../../services/post.service';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';



@Component({
  selector: 'app-post-list',
  imports: [CommonModule, RouterModule],
  templateUrl: './post-list.component.html',
  styleUrl: './post-list.component.css'
})
export class PostListComponent implements OnInit {
  posts: Post[] = [];

  constructor(private postService: PostService) {}
  isImage(fileUrl: string): boolean {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUrl);
}

isVideo(fileUrl: string): boolean {
  return /\.(mp4|webm|ogg)$/i.test(fileUrl);
}

  ngOnInit(): void {
    this.fetchPosts();
  }

  fetchPosts(): void {
    this.postService.getPosts().subscribe({
      next: (data) => this.posts = data,
      error: () => alert('Failed to fetch posts.')
    });
  }

}
