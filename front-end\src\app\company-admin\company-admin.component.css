/* Dashboard Header */
.dashboard-header {
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

/* Main Dashboard */
.dashboard-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* Welcome Card */
.welcome-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.welcome-card mat-card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  margin: 8px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-number.active {
  color: #4caf50;
}

.stat-number.completed {
  color: #2196f3;
}

.stat-number.pending {
  color: #ff9800;
}

.stat-number.approved {
  color: #4caf50;
}

.stat-number.rejected {
  color: #f44336;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content Section */
.content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1024px) {
  .content-section {
    grid-template-columns: 1fr;
  }
}

.content-card {
  height: fit-content;
}

.content-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Content List */
.content-list {
  max-height: 600px;
  overflow-y: auto;
}

.content-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: white;
  transition: box-shadow 0.2s ease;
}

.content-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.content-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
}

.content-description {
  color: #666;
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.content-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 12px 0;
  font-size: 12px;
  color: #666;
}

.content-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.content-meta mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Media Preview */
.media-preview {
  margin: 12px 0;
  text-align: center;
}

.media-preview img,
.media-preview video {
  max-width: 200px;
  max-height: 150px;
  border-radius: 4px;
  object-fit: cover;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.action-buttons button {
  flex: 1;
  min-width: 100px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Creators Overview */
.creators-overview-card {
  margin-bottom: 24px;
}

.creators-table-container {
  overflow-x: auto;
}

.creators-table {
  width: 100%;
  min-width: 600px;
}

.creator-info {
  display: flex;
  flex-direction: column;
}

.creator-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.creator-email {
  font-size: 12px;
  color: #666;
}

.post-count,
.pending-count {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.pending-count {
  position: relative;
}

/* Action Shortcuts */
.action-shortcuts-card {
  margin-bottom: 24px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-grid button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }

  .content-meta {
    flex-direction: column;
    gap: 8px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

/* Snackbar Styles */
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

/* Material Design Overrides */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.mat-mdc-chip {
  font-size: 12px !important;
}

.mat-mdc-chip mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* List Styles */
.mat-mdc-list-item {
  border-bottom: 1px solid #f0f0f0;
}

.mat-mdc-list-item:last-child {
  border-bottom: none;
}

/* Badge Positioning */
.mat-badge-content {
  font-size: 10px !important;
  font-weight: 600 !important;
}

/* Scrollbar Styling */
.content-list::-webkit-scrollbar {
  width: 6px;
}

.content-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}