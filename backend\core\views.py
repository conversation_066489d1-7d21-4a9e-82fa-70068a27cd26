from django.shortcuts import render
from rest_framework import viewsets, permissions
from .models import User, Company, Project, Post
from .serializers import UserSerializer, CompanySerializer, ProjectSerializer, PostSerializer
from django.http import JsonResponse
from django.contrib.auth import authenticate
from rest_framework.decorators import api_view,permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import CustomTokenObtainPairSerializer
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import exception_handler
from .serializers import UserSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication



# ViewSets
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.all()
    serializer_class = ProjectSerializer


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    serializer_class = PostSerializer

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

# JWT login endpoint
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_current_user(request):
    serializer = UserSerializer(request.user)
    return Response(serializer.data)

@api_view(['POST'])
def login_view(request):
    username = request.data.get('username')
    password = request.data.get('password')
    user = authenticate(username=username, password=password)

    if user:
        refresh = RefreshToken.for_user(user)
        return JsonResponse({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'username': user.username,
            'role': user.role
        })
    return JsonResponse({'error': 'Invalid credentials'}, status=401)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_posts(request):
    user = request.user
    if user.role == 'creator':
        posts = Post.objects.filter(creator=user)
    elif user.role == 'company_admin':
        posts = Post.objects.filter(project__company=user.company)
    else:
        posts = Post.objects.all()
    
    serializer = PostSerializer(posts, many=True, context={'request': request})
    return Response(serializer.data)

class SuperAdminViewSet(viewsets.ViewSet):
    
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]


    # List companies
    @action(detail=False, methods=['get'])
    def list_companies(self, request):
        companies = Company.objects.all()
        serializer = CompanySerializer(companies, many=True)
        return Response(serializer.data)
    
    # Add a new company
    @action(detail=False, methods=['post'])
    def add_company(self, request):
        serializer = CompanySerializer(data=request.data)
        if serializer.is_valid():
            company = serializer.save()
            return Response({"message": "Company created successfully", "company": serializer.data})
        return Response(serializer.errors, status=400)
    
    # Add a project to a company
    @action(detail=True, methods=['post'])
    def add_project(self, request, pk=None):
        try:
            company = Company.objects.get(id=pk)
        except Company.DoesNotExist:
            return Response({"error": "Company not found."}, status=404)

        project_data = request.data
        project_data['company'] = company.id
        serializer = ProjectSerializer(data=project_data)

        if serializer.is_valid():
            serializer.save()

            # Fetch and return updated project list
            projects = Project.objects.filter(company=company)
            project_list_serializer = ProjectSerializer(projects, many=True)

            return Response({
                "message": "Project added successfully",
                "projects": project_list_serializer.data
            })
        return Response(serializer.errors, status=400)

    # Assign a content creator to a project
    @action(detail=True, methods=['post'], url_path='assign_creator')
    def assign_creator(self, request, pk=None):
        try:
            project_id = pk
            creator_id = request.data.get('creator_id')

            project = Project.objects.get(id=project_id)
            creator = User.objects.get(id=creator_id)
            project.creators.add(creator)

            return Response({"message": "Creator assigned successfully."}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    # ✅ List projects under a company
    @action(detail=False, methods=['get'], url_path='list_projects/(?P<company_id>[^/.]+)')
    def list_projects(self, request, company_id=None):
        try:
            company = Company.objects.get(id=company_id)
            projects = Project.objects.filter(company=company)

            project_data = []
            for project in projects:
                creators = project.creators.all()
                creators_data = [{'id': c.id, 'name': c.get_full_name()} for c in creators]
                project_data.append({
                    'id': project.id,
                    'name': project.name,
                    'creators_detail': creators_data
                })

            return Response(project_data)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        
class CreatorDashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def my_projects(self, request):
        user = request.user
        projects = Project.objects.filter(creators=user)
        data = []
        for project in projects:
            data.append({
                'id': project.id,
                'name': project.name,
                'company': project.company.name
            })
        return Response(data)

    @action(detail=False, methods=['get'])
    def my_posts(self, request):
        posts = Post.objects.filter(creator=request.user)
        serializer = PostSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)


class CompanyAdminViewSet(viewsets.ViewSet):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _check_company_admin_permission(self, request):
        """Check if user is company admin and has access to company data"""
        if request.user.role != 'company_admin':
            return Response({'error': 'Access denied. Company admin role required.'}, status=403)
        if not request.user.company:
            return Response({'error': 'No company associated with this admin.'}, status=400)
        return None

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get dashboard statistics for company admin"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company

        # Project statistics
        total_projects = Project.objects.filter(company=company).count()
        active_projects = Project.objects.filter(company=company, posts__status__in=['draft', 'submitted', 'scheduled']).distinct().count()
        completed_projects = Project.objects.filter(company=company, posts__status='posted').distinct().count()

        # Content statistics
        pending_reviews = Post.objects.filter(project__company=company, status='submitted').count()
        total_posts = Post.objects.filter(project__company=company).count()
        approved_posts = Post.objects.filter(project__company=company, status='posted').count()
        rejected_posts = Post.objects.filter(project__company=company, status='rejected').count()

        # Creator statistics
        total_creators = User.objects.filter(role='creator', assigned_projects__company=company).distinct().count()

        return Response({
            'company_name': company.name,
            'admin_name': request.user.get_full_name() or request.user.username,
            'projects': {
                'total': total_projects,
                'active': active_projects,
                'completed': completed_projects
            },
            'content': {
                'total_posts': total_posts,
                'pending_reviews': pending_reviews,
                'approved': approved_posts,
                'rejected': rejected_posts
            },
            'creators': {
                'total': total_creators
            }
        })

    @action(detail=False, methods=['get'])
    def recent_content(self, request):
        """Get recent content submissions for the company"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        recent_posts = Post.objects.filter(
            project__company=company
        ).order_by('-scheduled_time')[:10]

        serializer = PostSerializer(recent_posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pending_reviews(self, request):
        """Get content pending approval"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        pending_posts = Post.objects.filter(
            project__company=company,
            status='submitted'
        ).order_by('-scheduled_time')

        serializer = PostSerializer(pending_posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def creators(self, request):
        """Get all creators assigned to company projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company
        creators = User.objects.filter(
            role='creator',
            assigned_projects__company=company
        ).distinct()

        creators_data = []
        for creator in creators:
            # Get creator's projects in this company
            creator_projects = Project.objects.filter(company=company, creators=creator)
            # Get creator's recent posts
            recent_posts_count = Post.objects.filter(
                creator=creator,
                project__company=company
            ).count()
            pending_posts_count = Post.objects.filter(
                creator=creator,
                project__company=company,
                status='submitted'
            ).count()

            creators_data.append({
                'id': creator.id,
                'username': creator.username,
                'full_name': creator.get_full_name() or creator.username,
                'email': creator.email,
                'projects': [{'id': p.id, 'name': p.name} for p in creator_projects],
                'total_posts': recent_posts_count,
                'pending_posts': pending_posts_count,
                'status': 'active' if pending_posts_count > 0 else 'idle'
            })

        return Response(creators_data)

    @action(detail=True, methods=['post'])
    def approve_content(self, request, pk=None):
        """Approve a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'posted'
            post.save()

            return Response({
                'message': 'Content approved successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for approval'}, status=404)

    @action(detail=True, methods=['post'])
    def reject_content(self, request, pk=None):
        """Reject a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'rejected'
            post.save()

            return Response({
                'message': 'Content rejected successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for rejection'}, status=404)

    @action(detail=True, methods=['post'])
    def request_changes(self, request, pk=None):
        """Request changes to a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company=request.user.company,
                status='submitted'
            )
            post.status = 'rework'
            post.save()

            return Response({
                'message': 'Changes requested successfully',
                'post_id': post.id,
                'new_status': post.status
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for changes'}, status=404)
    

    
def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    if response is not None:
        print(" Validation error:", response.data)
    return response

def health_check(request):
    return JsonResponse({"status": "ok"})