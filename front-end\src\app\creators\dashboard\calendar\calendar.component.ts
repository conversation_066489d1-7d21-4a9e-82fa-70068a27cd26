import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CalendarOptions } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { FullCalendarWrapperModule } from '../full-calendar-wrapper.module';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { PostDialogComponent } from '../post-dialog/post-dialog.component'; 
import { PostService } from '../../services/post.service';
import { OnInit } from '@angular/core';
// At the top of your calendar.component.ts
// @ViewChild('calendar') calendarComponent: FullCalendarComponent;


@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [
    CommonModule,
    FullCalendarWrapperModule,
    MatDialogModule  
  ],
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.css']
})
export class CalendarComponent implements OnInit {
  posts: any[] = [];

  getMediaUrl(imageUrl: string): string {
  return imageUrl || '';
}


  constructor(private dialog: MatDialog,private postService: PostService) {} 
  ngOnInit(): void {
    this.loadPosts();
  }

  loadPosts(): void {
  this.postService.getAllPosts().subscribe(posts => {
    this.posts = posts;
    const calendarApi = (document.querySelector('.fc') as any)?.__ngContext__[8]?.calendar;
    // const calendarApi = this.calendarComponent?.getApi();
    

    if (calendarApi) {
      calendarApi.removeAllEvents();
      posts.forEach(post => {
        calendarApi.addEvent({
          title: post.title,
          start: post.scheduled_date,
          allDay: true,
          extendedProps: {
            imageUrl: this.getMediaUrl(post.media_url)
          }
        });
      });
    }
  });
}

  calendarOptions: CalendarOptions = {
  plugins: [dayGridPlugin, interactionPlugin],
  initialView: 'dayGridMonth',
  editable: true,
  selectable: true,
  events: (info, successCallback, failureCallback) => {
    this.postService.getAllPosts().subscribe(posts => {
      this.posts = posts;
      const events = posts.map(post => ({
        title: post.title,
        start: post.scheduled_date,
        allDay: true,
        extendedProps: {
          imageUrl: this.getMediaUrl(post.media_url),
        }
      }));
      successCallback(events);
    }, error => {
      failureCallback(error);
    });
  },
  dateClick: this.handleDateClick.bind(this),
  eventClick: this.handleEventClick.bind(this),
  eventDrop: this.handleEventDrop.bind(this),
  eventResize: this.handleEventResize.bind(this),
  eventContent: this.renderEventContent.bind(this)
};

  
  handleDateClick(arg: any) {
    const selectedProjectId = 1; 
    const currentUserId = 2;
    const dialogRef = this.dialog.open(PostDialogComponent, {
      width: '400px',
      data: { 
        date: arg.dateStr,
        projectId: selectedProjectId,
        // creatorId: currentUserId
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const calendarApi = arg.view.calendar;
        const imageUrl = result.media ? URL.createObjectURL(result.media) : null;

        calendarApi.addEvent({
          title: result.title,
          start: result.date,
          allDay: true,
          extendedProps: {
            
          imageUrl: imageUrl
        }
        });

        this.posts.push(result); 
        console.log('Post data:', result);
      }
    });
  }
//   handleDateClick(arg: any) {
//   const selectedProjectId = 1;
//   const dialogRef = this.dialog.open(PostDialogComponent, {
//     width: '400px',
//     data: {
//       date: arg.dateStr,
//       projectId: selectedProjectId,
//     }
//   });

//   dialogRef.afterClosed().subscribe(result => {
//     if (result) {
//       // Once the post is saved to backend, refresh calendar with actual image URL
//       this.loadPosts();
//     }
//   });
// }


  getPreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }


  handleEventClick(arg: any) {
    const eventTitle = arg.event.title;
    const confirmDelete = confirm(`Selected: "${eventTitle}".\nDo you want to delete this event?`);

    if (confirmDelete) {
      arg.event.remove();
    }
  }

  handleEventDrop(info: any) {
    const event = info.event;
    alert(`Event "${event.title}" moved to ${event.startStr}`);
  }

  handleEventResize(info: any) {
    const event = info.event;
    alert(`Event "${event.title}" resized to end on ${event.endStr}`);
  }
  renderEventContent(arg: any) {
  const { title, extendedProps } = arg.event;
  const imageUrl = extendedProps?.imageUrl;

  return {
    html: `
      <div style="text-align:center; overflow: hidden;">
        ${imageUrl ? `<img src="${imageUrl}" style="width: 100%; height: 70px; object-fit: cover; border-radius: 4px;" />` : ''}
        <div style="font-weight: 500; font-size: 12px; padding-top: 4px;">${title}</div>
      </div>
    `
  };
}



}
