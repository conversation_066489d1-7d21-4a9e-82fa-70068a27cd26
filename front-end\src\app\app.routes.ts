import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { SuperAdminComponent } from './super-admin/super-admin.component';
import { CompanyAdminComponent } from './company-admin/company-admin.component';
import { DashboardComponent } from './creators/dashboard/dashboard.component';
import { AuthGuard } from './auth.guard';  
import { CreatePostComponent } from './creators/dashboard/create-post/create-post.component';
import { PostListComponent } from './creators/dashboard/post-list/post-list.component';
import { PostDetailComponent } from './creators/dashboard/post-detail/post-detail.component';
import { CalendarComponent } from './creators/dashboard/calendar/calendar.component';


export const routes: Routes = [
  { path: 'super-admin', component: SuperAdminComponent, canActivate: [AuthGuard] },
  { path: 'company-admin', component: CompanyAdminComponent, canActivate: [AuthGuard] },
  {
    path: 'creators',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'calendar',
        component: CalendarComponent
      },
      {
        path: 'create-post',
        component: CreatePostComponent
      },
      {
        path: 'post-list',
        component: PostListComponent
      },
      {
        path: 'post/:id',
        component: PostDetailComponent
      },
      {
        path: '',
        redirectTo: 'calendar',
        pathMatch: 'full'
      }
    ]
  },
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: '**', redirectTo: 'login' }
];
