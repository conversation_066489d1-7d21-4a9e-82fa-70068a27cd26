export interface DashboardStats {
  company_name: string;
  admin_name: string;
  projects: {
    total: number;
    active: number;
    completed: number;
  };
  content: {
    total_posts: number;
    pending_reviews: number;
    approved: number;
    rejected: number;
  };
  creators: {
    total: number;
  };
}

export interface ContentPost {
  id: number;
  title: string;
  description: string;
  media_url?: string;
  scheduled_date?: string;
  scheduled_time: string;
  status: 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled';
  creator: number;
  project: number;
  project_name?: string;
  creator_name?: string;
}

export interface Creator {
  id: number;
  username: string;
  full_name: string;
  email: string;
  projects: Array<{
    id: number;
    name: string;
  }>;
  total_posts: number;
  pending_posts: number;
  status: 'active' | 'idle';
}

export interface ActionResponse {
  message: string;
  post_id: number;
  new_status: string;
}

export interface NotificationItem {
  id: number;
  type: 'pending_review' | 'content_resubmission' | 'creator_feedback';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  post_id?: number;
  creator_id?: number;
}
