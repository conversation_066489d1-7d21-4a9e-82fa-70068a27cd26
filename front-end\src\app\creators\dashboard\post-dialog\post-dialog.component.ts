import { Component, Inject, ChangeDetectorRef } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { PostService } from '../../services/post.service'; 

@Component({
  selector: 'app-post-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatInputModule,
    MatButtonModule,
  ],
  templateUrl: './post-dialog.component.html'
})
export class PostDialogComponent {
  title = '';
  content = '';
  file: File | null = null;
  previewUrl: string | null = null;

  constructor(
    private postService: PostService,
    public dialogRef: MatDialogRef<PostDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private cdr: ChangeDetectorRef   // Inject ChangeDetectorRef here
  ) {}

  onFileChange(event: any): void {
    const selected = event.target.files[0];
    if (selected) {
      this.file = selected;

      // Generate preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result as string;
        this.cdr.detectChanges();  // Manually trigger change detection
      };
      reader.readAsDataURL(selected);
    }
  }

  onSubmit(form: NgForm): void {
    if (!form.valid || !this.file) {
      alert('Please fill out the form and upload a file.');
      return;
    }

    const formData = new FormData();
    formData.append('title', form.value.title);
    formData.append('description', form.value.content);
    formData.append('date', this.data.date);
    formData.append('media', this.file);
    formData.append('scheduled_date', this.data.date); 
    formData.append('project', this.data.projectId.toString());   

    this.postService.createPostWithFiles(formData).subscribe({
      next: (response) => {
        console.log('Saved to backend:', response);
        this.dialogRef.close({
          ...form.value,
          media: this.file,
          previewUrl: this.previewUrl,
          date: this.data.date
        });
      },
      error: (err) => {
        console.error('Failed to save post:', err);
        alert('Failed to save post.');
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
