.container {
    max-width: 900px;
    margin: 40px auto;
    padding: 24px;
    background-color: #f9f9f9;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    font-family: "Segoe UI", sans-serif;
  }
  
  h2, h3, h4 {
    color: #333;
  }
  
  input, select {
    padding: 10px;
    margin: 8px 0;
    border-radius: 8px;
    border: 1px solid #ccc;
    width: 100%;
    box-sizing: border-box;
  }
  
  button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  button:disabled {
    background-color: #9e9e9e;
    cursor: not-allowed;
  }
  
  button:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  .section {
    margin-bottom: 30px;
  }
  
  .company-block {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-left: 6px solid #4CAF50;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    transition: transform 0.2s ease;
  }
  
  .company-block:hover {
    transform: translateY(-2px);
  }
  
  .company-block h3 {
    margin-bottom: 8px;
  }
  
  .projects-section {
    margin-top: 16px;
    background-color: #f7f9fc;
    padding: 16px;
    border-radius: 10px;
    border: 1px dashed #b0bec5;
  }
  
  
  
  .add-project-form {
    margin-bottom: 20px;
  }
  
  ul {
    padding-left: 20px;
  }
  
  .project-item {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #ccc;
  }
  
  .assigned-creators ul {
    padding-left: 16px;
  }
  
  .assign-creator-form {
    margin-top: 12px;
  }
  
  .loading {
    text-align: center;
    font-size: 1.1rem;
    color: #888;
    margin-top: 20px;
  }
  
  .error {
    color: #e53935;
    font-weight: bold;
    margin-top: 16px;
  }
  