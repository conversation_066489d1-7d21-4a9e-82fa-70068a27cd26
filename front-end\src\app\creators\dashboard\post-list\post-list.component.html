<div *ngFor="let post of posts" class="post-card">
  <h3>{{ post.title }}</h3>
  <p>{{ post.description }}</p>
  <p><strong>Date:</strong> {{ post.scheduled_date | date }}</p>

  <ng-container *ngIf="post.media_url">
    <img *ngIf="isImage(post.media_url)" [src]="post.media_url" alt="Image" class="post-media" />
    <video *ngIf="isVideo(post.media_url)" [src]="post.media_url" controls class="post-media"></video>
  </ng-container>
</div>
