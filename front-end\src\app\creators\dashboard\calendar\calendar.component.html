<full-calendar [options]="calendarOptions"></full-calendar>


<div class="mt-4">
  <h3>Planned Posts</h3>
  <div *ngFor="let post of posts" class="p-3 mb-3 border rounded shadow-sm bg-light">
    <h5>{{ post.title }}</h5>
    <p>{{ post.content }}</p>
    <small><strong>Date:</strong> {{ post.scheduledDate | date:'medium' }}</small><br>

    <!-- Show image if imageUrl exists -->
    <ng-container *ngIf="post.media_url">
      <!-- <img
        [src]="getMediaUrl(post.imageUrl)"
        alt="Post Image"
        class="img-fluid mt-2 rounded"
        style="max-width: 100%; height: auto;"
      /> -->
      <img *ngIf="post.media_url" [src]="post.media_url" alt="Post media" />

    </ng-container>
  </div>
</div>
