import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Post } from '../models/post.model';
import { HttpHeaders } from '@angular/common/http';



@Injectable({
  providedIn: 'root'
})
export class PostService {
  private apiUrl = 'http://127.0.0.1:8000/api/posts/';

  constructor(private http: HttpClient) { }
  getPosts(): Observable<Post[]> {
    return this.http.get<Post[]>(this.apiUrl);
  }

  getPost(id: number): Observable<Post> {
    return this.http.get<Post>(`${this.apiUrl}${id}/`);
  }

  createPostWithFiles(formData: FormData): Observable<Post> {
  const token = localStorage.getItem('access_token');
  const headers = new HttpHeaders({
    'Authorization': `Bearer ${token}`
  });


  return this.http.post<Post>(
    'http://127.0.0.1:8000/api/creator-dashboard/upload_post/',
    formData,
    {headers}
    
  );
}



  updatePost(id: number, post: Post): Observable<Post> {
    return this.http.put<Post>(`${this.apiUrl}${id}/`, post);
  }

  deletePost(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}${id}/`);
  }

  getAllPosts(): Observable<any[]> {
  const token = localStorage.getItem('access_token'); // Adjust if stored under a different key
  const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
  return this.http.get<any[]>('http://127.0.0.1:8000/api/creator-dashboard/get_posts/', { headers });
}




}
