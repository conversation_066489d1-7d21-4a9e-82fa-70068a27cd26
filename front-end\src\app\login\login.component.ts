import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  username = '';
  password = '';
  errorMessage: string | null = null;

  constructor(private http: HttpClient, private router: Router) {}

  onSubmit() {
    const loginData = { username: this.username, password: this.password };

    this.http.post<any>('http://127.0.0.1:8000/api/token/', loginData).subscribe({
      next: (res) => {
        if (res.access && res.refresh) {
          localStorage.setItem('access_token', res.access);
          localStorage.setItem('refresh_token', res.refresh);
          console.log('Stored token:', localStorage.getItem('access_token'));
          console.log('Access Token from backend:', res.access);


          // Decode token to get role info
          const userRole = this.getRoleFromToken(res.access);

          // Navigate based on role
          if (userRole === 'super_admin') {
            this.router.navigate(['/super-admin']);
          } else if (userRole === 'company_admin') {
            this.router.navigate(['/company-admin']);
          } else if (userRole === 'creator') {
            this.router.navigate(['/creators']);
          } else {
            this.errorMessage = 'Unknown role, access denied!';
          }
        } else {
          this.errorMessage = 'Invalid login response!';
        }
      },
      error: (err) => {
        this.errorMessage = 'Login failed! Please check your credentials.';
      }
    });
  }

  getRoleFromToken(token: string): string | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || null; // Ensure your Django backend includes the 'role' in the JWT payload
    } catch (e) {
      return null;
    }
  }
}
