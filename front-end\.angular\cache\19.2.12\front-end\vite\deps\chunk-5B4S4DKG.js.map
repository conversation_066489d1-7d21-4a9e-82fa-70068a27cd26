{"version": 3, "sources": ["../../../../../../node_modules/@angular/platform-browser/fesm2022/dom_renderer-DGKzginR.mjs", "../../../../../../node_modules/@angular/platform-browser/fesm2022/browser-X3l5Bmdq.mjs", "../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.10\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformServer, DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Inject, Injectable, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  _zone;\n  _plugins;\n  _eventNameToPlugin = new Map();\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static ɵfac = function EventManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: EventManager,\n    factory: EventManager.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  _doc;\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nclass SharedStylesHost {\n  doc;\n  appId;\n  nonce;\n  /**\n   * Provides usage information for active inline style content and associated HTML <style> elements.\n   * Embedded styles typically originate from the `styles` metadata of a rendered component.\n   */\n  inline = new Map();\n  /**\n   * Provides usage information for active external style URLs and the associated HTML <link> elements.\n   * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n   */\n  external = new Map();\n  /**\n   * Set of host DOM nodes that will have styles attached.\n   */\n  hosts = new Set();\n  /**\n   * Whether the application code is currently executing on a server.\n   */\n  isServer;\n  constructor(doc, appId, nonce, platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.isServer = isPlatformServer(platformId);\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles, urls) {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n    urls?.forEach(value => this.addUsage(value, this.external, createLinkElement));\n  }\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles, urls) {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n    urls?.forEach(value => this.removeUsage(value, this.external));\n  }\n  addUsage(value, usages, creator) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n      });\n    }\n  }\n  removeUsage(value, usages) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n  ngOnDestroy() {\n    for (const [, {\n      elements\n    }] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode) {\n    this.hosts.add(hostNode);\n    // Add existing styles to new host\n    for (const [style, {\n      elements\n    }] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {\n      elements\n    }] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n  removeHost(hostNode) {\n    this.hosts.delete(hostNode);\n  }\n  addElement(host, element) {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n    // Add application identifier when on the server to support client-side reuse\n    if (this.isServer) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n  static ɵfac = function SharedStylesHost_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedStylesHost,\n    factory: SharedStylesHost.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nclass DomRendererFactory2 {\n  eventManager;\n  sharedStylesHost;\n  appId;\n  removeStylesOnCompDestroy;\n  doc;\n  platformId;\n  ngZone;\n  nonce;\n  tracingService;\n  rendererByCompId = new Map();\n  defaultRenderer;\n  platformIsServer;\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.tracingService = tracingService;\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    this.rendererByCompId.delete(componentId);\n  }\n  static ɵfac = function DomRendererFactory2_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(_TracingService, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomRendererFactory2,\n    factory: DomRendererFactory2.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }, {\n    type: i0.ɵTracingService,\n    decorators: [{\n      type: Inject,\n      args: [_TracingService]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  eventManager;\n  doc;\n  ngZone;\n  platformIsServer;\n  tracingService;\n  data = Object.create(null);\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  destroyNode = null;\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = _getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if (this.tracingService?.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  hostEl;\n  shadowRoot;\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = component.getExternalStyles?.();\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  removeStylesOnCompDestroy;\n  styles;\n  styleUrls;\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = component.getExternalStyles?.(compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  contentAttr;\n  hostAttr;\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };\n", "/**\n * @license Angular v19.2.10\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter as _DomAdapter, ɵsetRootDomAdapter as _setRootDomAdapter, ɵparseCookieValue as _parseCookieValue, ɵgetDOM as _getDOM, DOCUMENT, CommonModule, XhrFactory, ɵPLATFORM_BROWSER_ID as _PLATFORM_BROWSER_ID } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal as _global, ɵRuntimeError as _RuntimeError, Injectable, Inject, createPlatformFactory, ɵinternalCreateApplication as _internalCreateApplication, inject, InjectionToken, ApplicationModule, ɵINJECTOR_SCOPE as _INJECTOR_SCOPE, ErrorHandler, RendererFactory2, ɵTESTABILITY_GETTER as _TESTABILITY_GETTER, NgZone, TestabilityRegistry, Testability, ɵTESTABILITY as _TESTABILITY, platformCore, PLATFORM_ID, PLATFORM_INITIALIZER, ɵsetDocument as _setDocument, NgModule } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS, DomRendererFactory2, SharedStylesHost, EventManager } from './dom_renderer-DGKzginR.mjs';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends _DomAdapter {\n  supportsDOMEvents = true;\n  static makeCurrent() {\n    _setRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener, options) {\n    el.addEventListener(evt, listener, options);\n    return () => {\n      el.removeEventListener(evt, listener, options);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    node.remove();\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return _parseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    _global['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new _RuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    _global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    _global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = _global['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!_global['frameworkStabilizers']) {\n      _global['frameworkStabilizers'] = [];\n    }\n    _global['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (_getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static ɵfac = function BrowserXhr_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserXhr)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserXhr,\n    factory: BrowserXhr.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler, options) {\n    element.addEventListener(eventName, handler, options);\n    return () => this.removeEventListener(element, eventName, handler, options);\n  }\n  removeEventListener(target, eventName, callback, options) {\n    return target.removeEventListener(eventName, callback, options);\n  }\n  static ɵfac = function DomEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomEventsPlugin,\n    factory: DomEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return _getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n  static ɵfac = function KeyEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: KeyEventsPlugin,\n    factory: KeyEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return _internalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return _internalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  _setDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: _PLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: _TESTABILITY_GETTER,\n  useClass: BrowserGetTestability\n}, {\n  provide: _TESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: _INJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n        optional: true,\n        skipSelf: true\n      });\n      if (providersAlreadyPresent) {\n        throw new _RuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n      }\n    }\n  }\n  static ɵfac = function BrowserModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserModule,\n    exports: [CommonModule, ApplicationModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n    imports: [CommonModule, ApplicationModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [], null);\n})();\nexport { BrowserDomAdapter, BrowserGetTestability, BrowserModule, DomEventsPlugin, KeyEventsPlugin, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport };\n", "/**\n * @license Angular v19.2.10\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { BrowserModule, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, KeyEventsPlugin as ɵKeyEventsPlugin } from './browser-X3l5Bmdq.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, ɵglobal as _global, ApplicationRef, InjectionToken, Injector, Optional, ɵConsole as _Console, NgModule, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, forwardRef, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ɵwithEventReplay as _withEventReplay, ɵwithI18nSupport as _withI18nSupport, ɵwithIncrementalHydration as _withIncrementalHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, Version } from '@angular/core';\nimport { EVENT_MANAGER_PLUGINS, EventManagerPlugin } from './dom_renderer-DGKzginR.mjs';\nexport { EventManager, REMOVE_STYLES_ON_COMPONENT_DESTROY, DomRendererFactory2 as ɵDomRendererFactory2, SharedStylesHost as ɵSharedStylesHost } from './dom_renderer-DGKzginR.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  _doc;\n  _dom;\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = _getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static ɵfac = function Meta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: Meta.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  _doc;\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static ɵfac = function Title_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: Title.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = _global['ng'] = _global['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  msPerTick;\n  numTicks;\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  appRef;\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (_getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events = [];\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides = {};\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options;\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGestureConfig)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  _config;\n  _injector;\n  loader;\n  _loaderPromise = null;\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(_Console);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(_Console);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(_Console);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n  static ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(__ngFactoryType__) {\n      let __ngConditionalFactory__ = null;\n      if (__ngFactoryType__) {\n        __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n      } else {\n        __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n      }\n      return __ngConditionalFactory__;\n    },\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return _unwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n      default:\n        throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return _bypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return _bypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return _bypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return _bypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return _bypassSanitizationTrustResourceUrl(value);\n  }\n  static ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: DomSanitizerImpl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @experimental\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(_ZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(-5e3 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], _withDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : _withHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.10');\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,wBAAwB,IAAI,eAAe,YAAY,wBAAwB,EAAE;AAOvF,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,qBAAqB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAI7B,YAAY,SAAS,OAAO;AAC1B,SAAK,QAAQ;AACb,YAAQ,QAAQ,YAAU;AACxB,aAAO,UAAU;AAAA,IACnB,CAAC;AACD,SAAK,WAAW,QAAQ,MAAM,EAAE,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,UAAM,SAAS,KAAK,eAAe,SAAS;AAC5C,WAAO,OAAO,iBAAiB,SAAS,WAAW,SAAS,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,QAAI,SAAS,KAAK,mBAAmB,IAAI,SAAS;AAClD,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK;AACrB,aAAS,QAAQ,KAAK,CAAAA,YAAUA,QAAO,SAAS,SAAS,CAAC;AAC1D,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,aAAc,OAAkD,OAAO,cAAc,eAAe,cAAc,2CAA2C,SAAS,EAAE;AAAA,IACpL;AACA,SAAK,mBAAmB,IAAI,WAAW,MAAM;AAC7C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAiB,SAAS,qBAAqB,GAAM,SAAY,MAAM,CAAC;AAAA,EAC3G;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AASH,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA;AACF;AAGA,IAAM,wBAAwB;AAK9B,SAAS,eAAe,UAAU;AAChC,aAAW,WAAW,UAAU;AAC9B,YAAQ,OAAO;AAAA,EACjB;AACF;AAOA,SAAS,mBAAmB,OAAO,KAAK;AACtC,QAAM,eAAe,IAAI,cAAc,OAAO;AAC9C,eAAa,cAAc;AAC3B,SAAO;AACT;AASA,SAAS,gBAAgB,KAAK,OAAO,QAAQ,UAAU;AACrD,QAAM,WAAW,IAAI,MAAM,iBAAiB,SAAS,qBAAqB,KAAK,KAAK,WAAW,qBAAqB,KAAK,KAAK,IAAI;AAClI,MAAI,UAAU;AACZ,eAAW,gBAAgB,UAAU;AACnC,mBAAa,gBAAgB,qBAAqB;AAClD,UAAI,wBAAwB,iBAAiB;AAG3C,iBAAS,IAAI,aAAa,KAAK,MAAM,aAAa,KAAK,YAAY,GAAG,IAAI,CAAC,GAAG;AAAA,UAC5E,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,QACzB,CAAC;AAAA,MACH,WAAW,aAAa,aAAa;AACnC,eAAO,IAAI,aAAa,aAAa;AAAA,UACnC,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,kBAAkB,KAAK,KAAK;AACnC,QAAM,cAAc,IAAI,cAAc,MAAM;AAC5C,cAAY,aAAa,OAAO,YAAY;AAC5C,cAAY,aAAa,QAAQ,GAAG;AACpC,SAAO;AACT;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,WAAW,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAInB,QAAQ,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAIhB;AAAA,EACA,YAAY,KAAK,OAAO,OAAO,aAAa,CAAC,GAAG;AAC9C,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW,iBAAiB,UAAU;AAC3C,oBAAgB,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ;AACtD,SAAK,MAAM,IAAI,IAAI,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ,MAAM;AACtB,eAAW,SAAS,QAAQ;AAC1B,WAAK,SAAS,OAAO,KAAK,QAAQ,kBAAkB;AAAA,IACtD;AACA,UAAM,QAAQ,WAAS,KAAK,SAAS,OAAO,KAAK,UAAU,iBAAiB,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ,MAAM;AACzB,eAAW,SAAS,QAAQ;AAC1B,WAAK,YAAY,OAAO,KAAK,MAAM;AAAA,IACrC;AACA,UAAM,QAAQ,WAAS,KAAK,YAAY,OAAO,KAAK,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,SAAS,OAAO,QAAQ,SAAS;AAE/B,UAAM,SAAS,OAAO,IAAI,KAAK;AAE/B,QAAI,QAAQ;AACV,WAAK,OAAO,cAAc,eAAe,cAAc,OAAO,UAAU,GAAG;AAGzE,eAAO,SAAS,QAAQ,aAAW,QAAQ,aAAa,mBAAmB,EAAE,CAAC;AAAA,MAChF;AACA,aAAO;AAAA,IACT,OAAO;AAEL,aAAO,IAAI,OAAO;AAAA,QAChB,OAAO;AAAA,QACP,UAAU,CAAC,GAAG,KAAK,KAAK,EAAE,IAAI,UAAQ,KAAK,WAAW,MAAM,QAAQ,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,MACvF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,OAAO,QAAQ;AAEzB,UAAM,SAAS,OAAO,IAAI,KAAK;AAG/B,QAAI,QAAQ;AACV,aAAO;AACP,UAAI,OAAO,SAAS,GAAG;AACrB,uBAAe,OAAO,QAAQ;AAC9B,eAAO,OAAO,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,eAAW,CAAC,EAAE;AAAA,MACZ;AAAA,IACF,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG;AACxC,qBAAe,QAAQ;AAAA,IACzB;AACA,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,UAAU;AAChB,SAAK,MAAM,IAAI,QAAQ;AAEvB,eAAW,CAAC,OAAO;AAAA,MACjB;AAAA,IACF,CAAC,KAAK,KAAK,QAAQ;AACjB,eAAS,KAAK,KAAK,WAAW,UAAU,mBAAmB,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,IAC9E;AACA,eAAW,CAAC,KAAK;AAAA,MACf;AAAA,IACF,CAAC,KAAK,KAAK,UAAU;AACnB,eAAS,KAAK,KAAK,WAAW,UAAU,kBAAkB,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,WAAW,UAAU;AACnB,SAAK,MAAM,OAAO,QAAQ;AAAA,EAC5B;AAAA,EACA,WAAW,MAAM,SAAS;AAExB,QAAI,KAAK,OAAO;AACd,cAAQ,aAAa,SAAS,KAAK,KAAK;AAAA,IAC1C;AAEA,QAAI,KAAK,UAAU;AACjB,cAAQ,aAAa,uBAAuB,KAAK,KAAK;AAAA,IACxD;AAEA,WAAO,KAAK,YAAY,OAAO;AAAA,EACjC;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,GAAM,SAAS,MAAM,GAAM,SAAS,WAAW,CAAC,GAAM,SAAS,WAAW,CAAC;AAAA,EACpJ;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iBAAiB;AAAA,EACrB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AACV;AACA,IAAM,kBAAkB;AACxB,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,YAAY,WAAW,kBAAkB;AAC/C,IAAM,eAAe,cAAc,kBAAkB;AAIrD,IAAM,6CAA6C;AAQnD,IAAM,qCAAqC,IAAI,eAAe,YAAY,8BAA8B,IAAI;AAAA,EAC1G,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,SAAS,qBAAqB,kBAAkB;AAC9C,SAAO,aAAa,QAAQ,iBAAiB,gBAAgB;AAC/D;AACA,SAAS,kBAAkB,kBAAkB;AAC3C,SAAO,UAAU,QAAQ,iBAAiB,gBAAgB;AAC5D;AACA,SAAS,kBAAkB,QAAQ,QAAQ;AACzC,SAAO,OAAO,IAAI,OAAK,EAAE,QAAQ,iBAAiB,MAAM,CAAC;AAC3D;AAmBA,SAAS,0BAA0B,UAAU,QAAQ;AACnD,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,IAAI,IAAI,UAAU,kBAAkB;AAChE,SAAO,OAAO,IAAI,gBAAc;AAC9B,QAAI,CAAC,WAAW,SAAS,mBAAmB,GAAG;AAC7C,aAAO;AAAA,IACT;AACA,WAAO,WAAW,QAAQ,sBAAsB,CAAC,GAAG,iBAAiB;AACnE,UAAI,aAAa,CAAC,MAAM,OAAO,aAAa,WAAW,OAAO,KAAK,gBAAgB,KAAK,YAAY,GAAG;AACrG,eAAO,wBAAwB,YAAY;AAAA,MAC7C;AACA,YAAM;AAAA,QACJ,UAAU;AAAA,MACZ,IAAI,IAAI,IAAI,cAAc,mBAAmB;AAC7C,aAAO,wBAAwB,oBAAoB;AAAA,IACrD,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,oBAAI,IAAI;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,OAAO,2BAA2B,KAAK,YAAY,QAAQ,QAAQ,MAAM,iBAAiB,MAAM;AAC1I,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,SAAK,4BAA4B;AACjC,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,iBAAiB,UAAU;AACnD,SAAK,kBAAkB,IAAI,oBAAoB,cAAc,KAAK,QAAQ,KAAK,kBAAkB,KAAK,cAAc;AAAA,EACtH;AAAA,EACA,eAAe,SAAS,MAAM;AAC5B,QAAI,CAAC,WAAW,CAAC,MAAM;AACrB,aAAO,KAAK;AAAA,IACd;AACA,QAAI,KAAK,oBAAoB,KAAK,kBAAkB,kBAAkB,WAAW;AAE/E,aAAO,iCACF,OADE;AAAA,QAEL,eAAe,kBAAkB;AAAA,MACnC;AAAA,IACF;AACA,UAAM,WAAW,KAAK,oBAAoB,SAAS,IAAI;AAGvD,QAAI,oBAAoB,mCAAmC;AACzD,eAAS,YAAY,OAAO;AAAA,IAC9B,WAAW,oBAAoB,8BAA8B;AAC3D,eAAS,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAAS,MAAM;AACjC,UAAM,mBAAmB,KAAK;AAC9B,QAAI,WAAW,iBAAiB,IAAI,KAAK,EAAE;AAC3C,QAAI,CAAC,UAAU;AACb,YAAM,MAAM,KAAK;AACjB,YAAM,SAAS,KAAK;AACpB,YAAM,eAAe,KAAK;AAC1B,YAAM,mBAAmB,KAAK;AAC9B,YAAM,4BAA4B,KAAK;AACvC,YAAM,mBAAmB,KAAK;AAC9B,YAAM,iBAAiB,KAAK;AAC5B,cAAQ,KAAK,eAAe;AAAA,QAC1B,KAAK,kBAAkB;AACrB,qBAAW,IAAI,kCAAkC,cAAc,kBAAkB,MAAM,KAAK,OAAO,2BAA2B,KAAK,QAAQ,kBAAkB,cAAc;AAC3K;AAAA,QACF,KAAK,kBAAkB;AACrB,iBAAO,IAAI,kBAAkB,cAAc,kBAAkB,SAAS,MAAM,KAAK,QAAQ,KAAK,OAAO,kBAAkB,cAAc;AAAA,QACvI;AACE,qBAAW,IAAI,6BAA6B,cAAc,kBAAkB,MAAM,2BAA2B,KAAK,QAAQ,kBAAkB,cAAc;AAC1J;AAAA,MACJ;AACA,uBAAiB,IAAI,KAAK,IAAI,QAAQ;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAa;AAC7B,SAAK,iBAAiB,OAAO,WAAW;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,SAAS,YAAY,GAAM,SAAS,gBAAgB,GAAM,SAAS,MAAM,GAAM,SAAS,kCAAkC,GAAM,SAAS,QAAQ,GAAM,SAAS,WAAW,GAAM,SAAY,MAAM,GAAM,SAAS,SAAS,GAAM,SAAS,gBAAiB,CAAC,CAAC;AAAA,EACxT;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAA0B;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,uBAAO,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,wBAAwB;AAAA,EACxB,YAAY,cAAc,KAAK,QAAQ,kBAAkB,gBAAgB;AACvE,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,EAAC;AAAA,EACX,cAAc;AAAA,EACd,cAAc,MAAM,WAAW;AAC7B,QAAI,WAAW;AAUb,aAAO,KAAK,IAAI,gBAAgB,eAAe,SAAS,KAAK,WAAW,IAAI;AAAA,IAC9E;AACA,WAAO,KAAK,IAAI,cAAc,IAAI;AAAA,EACpC;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,IAAI,cAAc,KAAK;AAAA,EACrC;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,IAAI,eAAe,KAAK;AAAA,EACtC;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,UAAM,eAAe,eAAe,MAAM,IAAI,OAAO,UAAU;AAC/D,iBAAa,YAAY,QAAQ;AAAA,EACnC;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU;AACvC,QAAI,QAAQ;AACV,YAAM,eAAe,eAAe,MAAM,IAAI,OAAO,UAAU;AAC/D,mBAAa,aAAa,UAAU,QAAQ;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY,SAAS,UAAU;AAC7B,aAAS,OAAO;AAAA,EAClB;AAAA,EACA,kBAAkB,gBAAgB,iBAAiB;AACjD,QAAI,KAAK,OAAO,mBAAmB,WAAW,KAAK,IAAI,cAAc,cAAc,IAAI;AACvF,QAAI,CAAC,IAAI;AACP,YAAM,IAAI,aAAc,QAAmD,OAAO,cAAc,eAAe,cAAc,iBAAiB,cAAc,8BAA8B;AAAA,IAC5L;AACA,QAAI,CAAC,iBAAiB;AACpB,SAAG,cAAc;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,IAAI,MAAM,OAAO,WAAW;AACvC,QAAI,WAAW;AACb,aAAO,YAAY,MAAM;AACzB,YAAM,eAAe,eAAe,SAAS;AAC7C,UAAI,cAAc;AAChB,WAAG,eAAe,cAAc,MAAM,KAAK;AAAA,MAC7C,OAAO;AACL,WAAG,aAAa,MAAM,KAAK;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,SAAG,aAAa,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,gBAAgB,IAAI,MAAM,WAAW;AACnC,QAAI,WAAW;AACb,YAAM,eAAe,eAAe,SAAS;AAC7C,UAAI,cAAc;AAChB,WAAG,kBAAkB,cAAc,IAAI;AAAA,MACzC,OAAO;AACL,WAAG,gBAAgB,GAAG,SAAS,IAAI,IAAI,EAAE;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,SAAG,gBAAgB,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,SAAS,IAAI,MAAM;AACjB,OAAG,UAAU,IAAI,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,IAAI,MAAM;AACpB,OAAG,UAAU,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS,IAAI,OAAO,OAAO,OAAO;AAChC,QAAI,SAAS,oBAAoB,WAAW,oBAAoB,YAAY;AAC1E,SAAG,MAAM,YAAY,OAAO,OAAO,QAAQ,oBAAoB,YAAY,cAAc,EAAE;AAAA,IAC7F,OAAO;AACL,SAAG,MAAM,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,IAAI,OAAO,OAAO;AAC5B,QAAI,QAAQ,oBAAoB,UAAU;AAExC,SAAG,MAAM,eAAe,KAAK;AAAA,IAC/B,OAAO;AACL,SAAG,MAAM,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,IAAI,MAAM,OAAO;AAC3B,QAAI,MAAM,MAAM;AACd;AAAA,IACF;AACA,KAAC,OAAO,cAAc,eAAe,cAAc,KAAK,yBAAyB,qBAAqB,MAAM,UAAU;AACtH,OAAG,IAAI,IAAI;AAAA,EACb;AAAA,EACA,SAAS,MAAM,OAAO;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,QAAQ,OAAO,UAAU,SAAS;AACvC,KAAC,OAAO,cAAc,eAAe,cAAc,KAAK,yBAAyB,qBAAqB,OAAO,UAAU;AACvH,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS,OAAQ,EAAE,qBAAqB,KAAK,KAAK,MAAM;AACxD,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,aAAc,OAAuD,OAAO,cAAc,eAAe,cAAc,4BAA4B,MAAM,cAAc,KAAK,EAAE;AAAA,MAC1L;AAAA,IACF;AACA,QAAI,kBAAkB,KAAK,uBAAuB,QAAQ;AAC1D,QAAI,KAAK,gBAAgB,mBAAmB;AAC1C,wBAAkB,KAAK,eAAe,kBAAkB,QAAQ,OAAO,eAAe;AAAA,IACxF;AACA,WAAO,KAAK,aAAa,iBAAiB,QAAQ,OAAO,iBAAiB,OAAO;AAAA,EACnF;AAAA,EACA,uBAAuB,cAAc;AAKnC,WAAO,WAAS;AAMd,UAAI,UAAU,gBAAgB;AAC5B,eAAO;AAAA,MACT;AAGA,YAAM,uBAAuB,KAAK,mBAAmB,KAAK,OAAO,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK;AAC3H,UAAI,yBAAyB,OAAO;AAClC,cAAM,eAAe;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,eAAe,MAAM,IAAI,WAAW,CAAC,GAAG;AAC9C,SAAS,qBAAqB,MAAM,UAAU;AAC5C,MAAI,KAAK,WAAW,CAAC,MAAM,aAAa;AACtC,UAAM,IAAI,aAAc,MAA2D,wBAAwB,QAAQ,IAAI,IAAI;AAAA;AAAA,+DAEhE,IAAI,iIAAiI;AAAA,EAClM;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,YAAY,cAAc,KAAK,YAAY;AACzD;AACA,IAAM,oBAAN,cAAgC,oBAAoB;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,QAAQ,WAAW,KAAK,QAAQ,OAAO,kBAAkB,gBAAgB;AACnH,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc;AACjE,SAAK,mBAAmB;AACxB,SAAK,SAAS;AACd,SAAK,aAAa,OAAO,aAAa;AAAA,MACpC,MAAM;AAAA,IACR,CAAC;AACD,SAAK,iBAAiB,QAAQ,KAAK,UAAU;AAC7C,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW;AAEb,YAAM,WAAW,OAAQ,EAAE,YAAY,GAAG,KAAK;AAC/C,eAAS,0BAA0B,UAAU,MAAM;AAAA,IACrD;AACA,aAAS,kBAAkB,UAAU,IAAI,MAAM;AAC/C,eAAW,SAAS,QAAQ;AAC1B,YAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,UAAI,OAAO;AACT,gBAAQ,aAAa,SAAS,KAAK;AAAA,MACrC;AACA,cAAQ,cAAc;AACtB,WAAK,WAAW,YAAY,OAAO;AAAA,IACrC;AAOA,UAAM,YAAY,UAAU,oBAAoB;AAChD,QAAI,WAAW;AACb,iBAAW,YAAY,WAAW;AAChC,cAAM,SAAS,kBAAkB,UAAU,GAAG;AAC9C,YAAI,OAAO;AACT,iBAAO,aAAa,SAAS,KAAK;AAAA,QACpC;AACA,aAAK,WAAW,YAAY,MAAM;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM;AACrB,WAAO,SAAS,KAAK,SAAS,KAAK,aAAa;AAAA,EAClD;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,WAAO,MAAM,YAAY,KAAK,iBAAiB,MAAM,GAAG,QAAQ;AAAA,EAClE;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU;AACvC,WAAO,MAAM,aAAa,KAAK,iBAAiB,MAAM,GAAG,UAAU,QAAQ;AAAA,EAC7E;AAAA,EACA,YAAY,SAAS,UAAU;AAC7B,WAAO,MAAM,YAAY,MAAM,QAAQ;AAAA,EACzC;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,iBAAiB,MAAM,WAAW,KAAK,iBAAiB,IAAI,CAAC,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU;AACR,SAAK,iBAAiB,WAAW,KAAK,UAAU;AAAA,EAClD;AACF;AACA,IAAM,+BAAN,cAA2C,oBAAoB;AAAA,EAC7D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,WAAW,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB,QAAQ;AACvI,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc;AACjE,SAAK,mBAAmB;AACxB,SAAK,4BAA4B;AACjC,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW;AAEb,YAAM,WAAW,OAAQ,EAAE,YAAY,GAAG,KAAK;AAC/C,eAAS,0BAA0B,UAAU,MAAM;AAAA,IACrD;AACA,SAAK,SAAS,SAAS,kBAAkB,QAAQ,MAAM,IAAI;AAC3D,SAAK,YAAY,UAAU,oBAAoB,MAAM;AAAA,EACvD;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,UAAU,KAAK,QAAQ,KAAK,SAAS;AAAA,EAC7D;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,2BAA2B;AACnC;AAAA,IACF;AACA,SAAK,iBAAiB,aAAa,KAAK,QAAQ,KAAK,SAAS;AAAA,EAChE;AACF;AACA,IAAM,oCAAN,cAAgD,6BAA6B;AAAA,EAC3E;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,WAAW,OAAO,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB;AACtI,UAAM,SAAS,QAAQ,MAAM,UAAU;AACvC,UAAM,cAAc,kBAAkB,WAAW,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB,MAAM;AACjI,SAAK,cAAc,qBAAqB,MAAM;AAC9C,SAAK,WAAW,kBAAkB,MAAM;AAAA,EAC1C;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,YAAY;AACjB,SAAK,aAAa,SAAS,KAAK,UAAU,EAAE;AAAA,EAC9C;AAAA,EACA,cAAc,QAAQ,MAAM;AAC1B,UAAM,KAAK,MAAM,cAAc,QAAQ,IAAI;AAC3C,UAAM,aAAa,IAAI,KAAK,aAAa,EAAE;AAC3C,WAAO;AAAA,EACT;AACF;;;ACnzBA,IAAM,oBAAN,MAAM,2BAA0B,WAAY;AAAA,EAC1C,oBAAoB;AAAA,EACpB,OAAO,cAAc;AACnB,sBAAmB,IAAI,mBAAkB,CAAC;AAAA,EAC5C;AAAA,EACA,YAAY,IAAI,KAAK,UAAU,SAAS;AACtC,OAAG,iBAAiB,KAAK,UAAU,OAAO;AAC1C,WAAO,MAAM;AACX,SAAG,oBAAoB,KAAK,UAAU,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,cAAc,IAAI,KAAK;AACrB,OAAG,cAAc,GAAG;AAAA,EACtB;AAAA,EACA,OAAO,MAAM;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EACA,cAAc,SAAS,KAAK;AAC1B,UAAM,OAAO,KAAK,mBAAmB;AACrC,WAAO,IAAI,cAAc,OAAO;AAAA,EAClC;AAAA,EACA,qBAAqB;AACnB,WAAO,SAAS,eAAe,mBAAmB,WAAW;AAAA,EAC/D;AAAA,EACA,qBAAqB;AACnB,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA,EAEA,qBAAqB,KAAK,QAAQ;AAChC,QAAI,WAAW,UAAU;AACvB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,YAAY;AACzB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ;AACrB,aAAO,IAAI;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,KAAK;AACf,UAAM,OAAO,mBAAmB;AAChC,WAAO,QAAQ,OAAO,OAAO,aAAa,IAAI;AAAA,EAChD;AAAA,EACA,mBAAmB;AACjB,kBAAc;AAAA,EAChB;AAAA,EACA,eAAe;AACb,WAAO,OAAO,UAAU;AAAA,EAC1B;AAAA,EACA,UAAU,MAAM;AACd,WAAO,iBAAkB,SAAS,QAAQ,IAAI;AAAA,EAChD;AACF;AACA,IAAI,cAAc;AAClB,SAAS,qBAAqB;AAC5B,gBAAc,eAAe,SAAS,cAAc,MAAM;AAC1D,SAAO,cAAc,YAAY,aAAa,MAAM,IAAI;AAC1D;AACA,SAAS,aAAa,KAAK;AAGzB,SAAO,IAAI,IAAI,KAAK,SAAS,OAAO,EAAE;AACxC;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,UAAU;AACpB,YAAQ,uBAAuB,IAAI,CAAC,MAAM,kBAAkB,SAAS;AACnE,YAAM,cAAc,SAAS,sBAAsB,MAAM,eAAe;AACxE,UAAI,eAAe,MAAM;AACvB,cAAM,IAAI,aAAc,OAAoD,OAAO,cAAc,eAAe,cAAc,yCAAyC;AAAA,MACzK;AACA,aAAO;AAAA,IACT;AACA,YAAQ,4BAA4B,IAAI,MAAM,SAAS,oBAAoB;AAC3E,YAAQ,2BAA2B,IAAI,MAAM,SAAS,mBAAmB;AACzE,UAAM,gBAAgB,cAAY;AAChC,YAAM,gBAAgB,QAAQ,4BAA4B,EAAE;AAC5D,UAAI,QAAQ,cAAc;AAC1B,YAAM,YAAY,WAAY;AAC5B;AACA,YAAI,SAAS,GAAG;AACd,mBAAS;AAAA,QACX;AAAA,MACF;AACA,oBAAc,QAAQ,iBAAe;AACnC,oBAAY,WAAW,SAAS;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,QAAQ,sBAAsB,GAAG;AACpC,cAAQ,sBAAsB,IAAI,CAAC;AAAA,IACrC;AACA,YAAQ,sBAAsB,EAAE,KAAK,aAAa;AAAA,EACpD;AAAA,EACA,sBAAsB,UAAU,MAAM,iBAAiB;AACrD,QAAI,QAAQ,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,SAAS,eAAe,IAAI;AACtC,QAAI,KAAK,MAAM;AACb,aAAO;AAAA,IACT,WAAW,CAAC,iBAAiB;AAC3B,aAAO;AAAA,IACT;AACA,QAAI,OAAQ,EAAE,aAAa,IAAI,GAAG;AAChC,aAAO,KAAK,sBAAsB,UAAU,KAAK,MAAM,IAAI;AAAA,IAC7D;AACA,WAAO,KAAK,sBAAsB,UAAU,KAAK,eAAe,IAAI;AAAA,EACtE;AACF;AAKA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,QAAQ;AACN,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,YAAW;AAAA,EACtB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,yBAAwB,mBAAmB;AAAA,EAC/C,YAAY,KAAK;AACf,UAAM,GAAG;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,SAAS,WAAW;AAClB,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,YAAQ,iBAAiB,WAAW,SAAS,OAAO;AACpD,WAAO,MAAM,KAAK,oBAAoB,SAAS,WAAW,SAAS,OAAO;AAAA,EAC5E;AAAA,EACA,oBAAoB,QAAQ,WAAW,UAAU,SAAS;AACxD,WAAO,OAAO,oBAAoB,WAAW,UAAU,OAAO;AAAA,EAChE;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,QAAQ,CAAC;AAAA,EACzE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,gBAAgB,CAAC,OAAO,WAAW,QAAQ,OAAO;AAGxD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,KAAM;AAAA,EACN,KAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AACT;AAIA,IAAM,uBAAuB;AAAA,EAC3B,OAAO,WAAS,MAAM;AAAA,EACtB,WAAW,WAAS,MAAM;AAAA,EAC1B,QAAQ,WAAS,MAAM;AAAA,EACvB,SAAS,WAAS,MAAM;AAC1B;AAIA,IAAM,kBAAN,MAAM,yBAAwB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,YAAY,KAAK;AACf,UAAM,GAAG;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,WAAW;AAClB,WAAO,iBAAgB,eAAe,SAAS,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,UAAM,cAAc,iBAAgB,eAAe,SAAS;AAC5D,UAAM,iBAAiB,iBAAgB,cAAc,YAAY,SAAS,GAAG,SAAS,KAAK,QAAQ,QAAQ,CAAC;AAC5G,WAAO,KAAK,QAAQ,QAAQ,EAAE,kBAAkB,MAAM;AACpD,aAAO,OAAQ,EAAE,YAAY,SAAS,YAAY,cAAc,GAAG,gBAAgB,OAAO;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,eAAe,WAAW;AAC/B,UAAM,QAAQ,UAAU,YAAY,EAAE,MAAM,GAAG;AAC/C,UAAM,eAAe,MAAM,MAAM;AACjC,QAAI,MAAM,WAAW,KAAK,EAAE,iBAAiB,aAAa,iBAAiB,UAAU;AACnF,aAAO;AAAA,IACT;AACA,UAAM,MAAM,iBAAgB,cAAc,MAAM,IAAI,CAAC;AACrD,QAAI,UAAU;AACd,QAAI,SAAS,MAAM,QAAQ,MAAM;AACjC,QAAI,SAAS,IAAI;AACf,YAAM,OAAO,QAAQ,CAAC;AACtB,gBAAU;AAAA,IACZ;AACA,kBAAc,QAAQ,kBAAgB;AACpC,YAAM,QAAQ,MAAM,QAAQ,YAAY;AACxC,UAAI,QAAQ,IAAI;AACd,cAAM,OAAO,OAAO,CAAC;AACrB,mBAAW,eAAe;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,eAAW;AACX,QAAI,MAAM,UAAU,KAAK,IAAI,WAAW,GAAG;AAEzC,aAAO;AAAA,IACT;AAIA,UAAM,SAAS,CAAC;AAChB,WAAO,cAAc,IAAI;AACzB,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,sBAAsB,OAAO,aAAa;AAC/C,QAAI,UAAU,QAAQ,MAAM,GAAG,KAAK,MAAM;AAC1C,QAAI,MAAM;AACV,QAAI,YAAY,QAAQ,OAAO,IAAI,IAAI;AACrC,gBAAU,MAAM;AAChB,YAAM;AAAA,IACR;AAEA,QAAI,WAAW,QAAQ,CAAC,QAAS,QAAO;AACxC,cAAU,QAAQ,YAAY;AAC9B,QAAI,YAAY,KAAK;AACnB,gBAAU;AAAA,IACZ,WAAW,YAAY,KAAK;AAC1B,gBAAU;AAAA,IACZ;AACA,kBAAc,QAAQ,kBAAgB;AACpC,UAAI,iBAAiB,SAAS;AAC5B,cAAM,iBAAiB,qBAAqB,YAAY;AACxD,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,eAAe;AAAA,QACxB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AACP,WAAO,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc,SAAS,SAAS,MAAM;AAC3C,WAAO,WAAS;AACd,UAAI,iBAAgB,sBAAsB,OAAO,OAAO,GAAG;AACzD,aAAK,WAAW,MAAM,QAAQ,KAAK,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,cAAc,SAAS;AAC5B,WAAO,YAAY,QAAQ,WAAW;AAAA,EACxC;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,QAAQ,CAAC;AAAA,EACzE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AA6DH,SAAS,qBAAqB,eAAe,SAAS;AACpD,SAAO,0BAA2B;AAAA,IAChC;AAAA,KACG,sBAAsB,OAAO,EACjC;AACH;AAaA,SAAS,kBAAkB,SAAS;AAClC,SAAO,0BAA2B,sBAAsB,OAAO,CAAC;AAClE;AACA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,cAAc,CAAC,GAAG,0BAA0B,GAAI,SAAS,aAAa,CAAC,CAAE;AAAA,IACzE,mBAAmB;AAAA,EACrB;AACF;AAYA,SAAS,kCAAkC;AAIzC,SAAO,CAAC,GAAG,qBAAqB;AAClC;AACA,SAAS,iBAAiB;AACxB,oBAAkB,YAAY;AAChC;AACA,SAAS,eAAe;AACtB,SAAO,IAAI,aAAa;AAC1B;AACA,SAAS,YAAY;AAEnB,cAAa,QAAQ;AACrB,SAAO;AACT;AACA,IAAM,sCAAsC,CAAC;AAAA,EAC3C,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AACT,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AACd,CAAC;AAOD,IAAM,kBAAkB,sBAAsB,cAAc,WAAW,mCAAmC;AAO1G,IAAM,kCAAkC,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,mCAAmC,EAAE;AAChJ,IAAM,wBAAwB,CAAC;AAAA,EAC7B,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM,CAAC,QAAQ,qBAAqB,kBAAmB;AACzD,GAAG;AAAA,EACD,SAAS;AAAA;AAAA,EAET,UAAU;AAAA,EACV,MAAM,CAAC,QAAQ,qBAAqB,kBAAmB;AACzD,CAAC;AACD,IAAM,2BAA2B,CAAC;AAAA,EAChC,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM,CAAC,QAAQ;AACjB,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM,CAAC,QAAQ;AACjB,GAAG,qBAAqB,kBAAkB,cAAc;AAAA,EACtD,SAAS;AAAA,EACT,aAAa;AACf,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AACZ,GAAG,OAAO,cAAc,eAAe,YAAY;AAAA,EACjD,SAAS;AAAA,EACT,UAAU;AACZ,IAAI,CAAC,CAAC;AAUN,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,0BAA0B,OAAO,iCAAiC;AAAA,QACtE,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,yBAAyB;AAC3B,cAAM,IAAI,aAAc,MAA2D,qKAA0K;AAAA,MAC/P;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,iBAAiB;AAAA,EAC3C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,GAAG,0BAA0B,GAAG,qBAAqB;AAAA,IACjE,SAAS,CAAC,cAAc,iBAAiB;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,GAAG,0BAA0B,GAAG,qBAAqB;AAAA,MACjE,SAAS,CAAC,cAAc,iBAAiB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACviBH,IAAM,OAAN,MAAM,MAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO,OAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,KAAK,gBAAgB,OAAO;AACjC,QAAI,CAAC,IAAK,QAAO;AACjB,WAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAM,gBAAgB,OAAO;AACnC,QAAI,CAAC,KAAM,QAAO,CAAC;AACnB,WAAO,KAAK,OAAO,CAAC,QAAQ,QAAQ;AAClC,UAAI,KAAK;AACP,eAAO,KAAK,KAAK,oBAAoB,KAAK,aAAa,CAAC;AAAA,MAC1D;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,cAAc;AACnB,QAAI,CAAC,aAAc,QAAO;AAC1B,WAAO,KAAK,KAAK,cAAc,QAAQ,YAAY,GAAG,KAAK;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,cAAc;AACpB,QAAI,CAAC,aAAc,QAAO,CAAC;AAC3B,UAAM,OAAoB,KAAK,KAAK,iBAAiB,QAAQ,YAAY,GAAG;AAC5E,WAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,KAAK,UAAU;AACvB,QAAI,CAAC,IAAK,QAAO;AACjB,eAAW,YAAY,KAAK,eAAe,GAAG;AAC9C,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,QAAI,MAAM;AACR,aAAO,KAAK,0BAA0B,KAAK,IAAI;AAAA,IACjD;AACA,WAAO,KAAK,oBAAoB,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,cAAc;AACtB,SAAK,iBAAiB,KAAK,OAAO,YAAY,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,QAAI,MAAM;AACR,WAAK,KAAK,OAAO,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB,MAAM,gBAAgB,OAAO;AAC/C,QAAI,CAAC,eAAe;AAClB,YAAM,WAAW,KAAK,eAAe,IAAI;AAIzC,YAAM,OAAO,KAAK,QAAQ,QAAQ,EAAE,OAAO,CAAAC,UAAQ,KAAK,oBAAoB,MAAMA,KAAI,CAAC,EAAE,CAAC;AAC1F,UAAI,SAAS,OAAW,QAAO;AAAA,IACjC;AACA,UAAM,UAAU,KAAK,KAAK,cAAc,MAAM;AAC9C,SAAK,0BAA0B,MAAM,OAAO;AAC5C,UAAM,OAAO,KAAK,KAAK,qBAAqB,MAAM,EAAE,CAAC;AACrD,SAAK,YAAY,OAAO;AACxB,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,KAAK,IAAI;AACjC,WAAO,KAAK,GAAG,EAAE,QAAQ,UAAQ,GAAG,aAAa,KAAK,eAAe,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AACtF,WAAO;AAAA,EACT;AAAA,EACA,eAAe,KAAK;AAClB,UAAM,OAAO,IAAI,OAAO,SAAS;AACjC,WAAO,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EAC9B;AAAA,EACA,oBAAoB,KAAK,MAAM;AAC7B,WAAO,OAAO,KAAK,GAAG,EAAE,MAAM,SAAO,KAAK,aAAa,KAAK,eAAe,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC;AAAA,EAC/F;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,cAAc,IAAI,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,WAAO,KAAK,qBAAqB,OAAS,SAAS,QAAQ,CAAC;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,MAAK;AAAA,IACd,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,gBAAgB;AAAA,EACpB,WAAW;AACb;AAYA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAU;AACjB,SAAK,KAAK,QAAQ,YAAY;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,cAAc,mBAAmB;AACtD,WAAO,KAAK,qBAAqB,QAAU,SAAS,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAM;AAAA,IACf,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AASH,SAAS,YAAY,MAAM,OAAO;AAChC,MAAI,OAAO,aAAa,eAAe,CAAC,UAAU;AAKhD,UAAM,KAAK,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC;AAC7C,OAAG,IAAI,IAAI;AAAA,EACb;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,YAAY,WAAW,UAAU;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AACF;AAKA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA,YAAY,KAAK;AACf,SAAK,SAAS,IAAI,SAAS,IAAI,cAAc;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,oBAAoB,QAAQ;AAC1B,UAAM,SAAS,UAAU,OAAO,QAAQ;AACxC,UAAM,cAAc;AAEpB,QAAI,UAAU,aAAa,WAAW,OAAO,QAAQ,YAAY,YAAY;AAC3E,cAAQ,QAAQ,WAAW;AAAA,IAC7B;AACA,UAAM,QAAQ,YAAY,IAAI;AAC9B,QAAI,WAAW;AACf,WAAO,WAAW,KAAK,YAAY,IAAI,IAAI,QAAQ,KAAK;AACtD,WAAK,OAAO,KAAK;AACjB;AAAA,IACF;AACA,UAAM,MAAM,YAAY,IAAI;AAC5B,QAAI,UAAU,gBAAgB,WAAW,OAAO,QAAQ,eAAe,YAAY;AACjF,cAAQ,WAAW,WAAW;AAAA,IAChC;AACA,UAAM,aAAa,MAAM,SAAS;AAClC,YAAQ,IAAI,OAAO,QAAQ,0BAA0B;AACrD,YAAQ,IAAI,GAAG,UAAU,QAAQ,CAAC,CAAC,eAAe;AAClD,WAAO,IAAI,0BAA0B,WAAW,QAAQ;AAAA,EAC1D;AACF;AACA,IAAM,uBAAuB;AAc7B,SAAS,iBAAiB,KAAK;AAC7B,cAAY,sBAAsB,IAAI,gBAAgB,GAAG,CAAC;AAC1D,SAAO;AACT;AAMA,SAAS,oBAAoB;AAC3B,cAAY,sBAAsB,IAAI;AACxC;AAOA,IAAM,KAAN,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASP,OAAO,MAAM;AACX,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,IAAI,UAAU;AACnB,WAAO,kBAAgB;AACrB,aAAO,aAAa,iBAAiB,OAAO,eAAe,aAAa,eAAe,QAAQ,IAAI;AAAA,IACrG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,MAAM;AACrB,WAAO,eAAa,UAAU,eAAe,QAAQ,IAAI,MAAM;AAAA,EACjE;AACF;AACA,SAAS,eAAe,GAAG,UAAU;AACnC,MAAI,OAAQ,EAAE,cAAc,CAAC,GAAG;AAC9B,WAAO,EAAE,WAAW,EAAE,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,EAAE,kBAAkB,QAAQ,KAAK,EAAE,yBAAyB,EAAE,sBAAsB,QAAQ;AAAA,EAChK;AACA,SAAO;AACT;AAKA,IAAM,cAAc;AAAA;AAAA,EAElB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,cAAc;AAAA,EACd,aAAa;AAAA,EACb,gBAAgB;AAAA;AAAA,EAEhB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA;AAAA,EAEb,OAAO;AAAA,EACP,aAAa;AACf;AAQA,IAAM,wBAAwB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,wBAAwB,EAAE;AAQ3H,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,iBAAiB,EAAE;AAM5G,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,SAAS;AACnB,UAAM,KAAK,IAAI,OAAO,SAAS,KAAK,OAAO;AAC3C,OAAG,IAAI,OAAO,EAAE,IAAI;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC;AACD,OAAG,IAAI,QAAQ,EAAE,IAAI;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC;AACD,eAAW,aAAa,KAAK,WAAW;AACtC,SAAG,IAAI,SAAS,EAAE,IAAI,KAAK,UAAU,SAAS,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,8BAA6B,mBAAmB;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY,KAAK,SAAS,WAAW,QAAQ;AAC3C,UAAM,GAAG;AACT,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,CAAC,YAAY,eAAe,UAAU,YAAY,CAAC,KAAK,CAAC,KAAK,cAAc,SAAS,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,UAAU,CAAC,KAAK,QAAQ;AAClC,UAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,cAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,iBAAS,KAAK,QAAQ,SAAS,kGAAuG;AAAA,MACxI;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,WAAW,SAAS;AAC5C,UAAM,OAAO,KAAK,QAAQ,QAAQ;AAClC,gBAAY,UAAU,YAAY;AAGlC,QAAI,CAAC,OAAO,UAAU,KAAK,QAAQ;AACjC,WAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,MAAM,KAAK,OAAO,CAAC;AAIvF,UAAI,qBAAqB;AACzB,UAAI,aAAa,MAAM;AACrB,6BAAqB;AAAA,MACvB;AACA,WAAK,kBAAkB,MAAM,KAAK,eAAe,KAAK,MAAM;AAE1D,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,qBAAS,KAAK,mEAAmE;AAAA,UACnF;AACA,uBAAa,MAAM;AAAA,UAAC;AACpB;AAAA,QACF;AACA,YAAI,CAAC,oBAAoB;AAIvB,uBAAa,KAAK,iBAAiB,SAAS,WAAW,OAAO;AAAA,QAChE;AAAA,MACF,CAAC,EAAE,MAAM,MAAM;AACb,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,gBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,mBAAS,KAAK,QAAQ,SAAS,qEAA0E;AAAA,QAC3G;AACA,qBAAa,MAAM;AAAA,QAAC;AAAA,MACtB,CAAC,CAAC;AAIF,aAAO,MAAM;AACX,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,KAAK,kBAAkB,MAAM;AAElC,YAAM,KAAK,KAAK,QAAQ,YAAY,OAAO;AAC3C,YAAM,WAAW,SAAU,UAAU;AACnC,aAAK,WAAW,WAAY;AAC1B,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AACA,SAAG,GAAG,WAAW,QAAQ;AACzB,aAAO,MAAM;AACX,WAAG,IAAI,WAAW,QAAQ;AAE1B,YAAI,OAAO,GAAG,YAAY,YAAY;AACpC,aAAG,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,WAAW;AACvB,WAAO,KAAK,QAAQ,OAAO,QAAQ,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,SAAS,QAAQ,GAAM,SAAS,qBAAqB,GAAM,SAAY,QAAQ,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,EAC3K;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAYH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,IACnF,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,MACnF,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiCH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,SAAS,qBAAqB,mBAAmB;AACxD,UAAI,2BAA2B;AAC/B,UAAI,mBAAmB;AACrB,mCAA2B,KAAK,qBAAqB,eAAc;AAAA,MACrE,OAAO;AACL,mCAA8B,SAAS,gBAAgB;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,aAAa,WAAW,MAAM,gBAAgB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,aAAa;AAAA,EAC1C;AAAA,EACA,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,QAAI,SAAS,KAAM,QAAO;AAC1B,YAAQ,KAAK;AAAA,MACX,KAAK,gBAAgB;AACnB,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA4B,GAAG;AACzE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,cAAe,KAAK,MAAM,OAAO,KAAK,CAAC,EAAE,SAAS;AAAA,MAC3D,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA8B,GAAG;AAC3E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAAgC,GAAG;AAC7E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAAyD,OAAO,cAAc,eAAe,cAAc,uCAAuC;AAAA,MAC5K,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0B,GAAG;AACvE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,aAAc,OAAO,KAAK,CAAC;AAAA,MACpC,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0C,GAAG;AACvF,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAA+D,OAAO,cAAc,eAAe,cAAc,oDAAoD,gBAAiB,GAAG;AAAA,MACnN;AACE,cAAM,IAAI,aAAc,OAA0D,OAAO,cAAc,eAAe,cAAc,8BAA8B,GAAG,SAAS,gBAAiB,GAAG;AAAA,IACtM;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,WAAO,4BAA6B,KAAK;AAAA,EAC3C;AAAA,EACA,yBAAyB,OAAO;AAC9B,WAAO,6BAA8B,KAAK;AAAA,EAC5C;AAAA,EACA,0BAA0B,OAAO;AAC/B,WAAO,8BAA+B,KAAK;AAAA,EAC7C;AAAA,EACA,uBAAuB,OAAO;AAC5B,WAAO,2BAA4B,KAAK;AAAA,EAC1C;AAAA,EACA,+BAA+B,OAAO;AACpC,WAAO,mCAAoC,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAI;AAAA,CACH,SAAUC,uBAAsB;AAC/B,EAAAA,sBAAqBA,sBAAqB,qBAAqB,IAAI,CAAC,IAAI;AACxE,EAAAA,sBAAqBA,sBAAqB,0BAA0B,IAAI,CAAC,IAAI;AAC7E,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,sBAAsB,IAAI,CAAC,IAAI;AAC3E,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAItD,SAAS,iBAAiB,OAAO,aAAa,CAAC,GAAG,WAAW,CAAC,GAAG;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,0BAA0B;AAGjC,SAAO,iBAAiB,qBAAqB,mBAAmB;AAClE;AASA,SAAS,6BAA6B,SAAS;AAE7C,SAAO,iBAAiB,qBAAqB,0BAA0B,sBAAuB,OAAO,CAAC;AACxG;AAOA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAkBA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAiBA,SAASC,4BAA2B;AAClC,SAAO,iBAAiB,qBAAqB,sBAAsB,yBAA0B,CAAC;AAChG;AAMA,SAAS,qCAAqC;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU,MAAM;AACd,YAAM,SAAS,OAAO,MAAM;AAC5B,YAAM,aAAa,OAAO,gBAAiB;AAG3C,UAAI,CAAC,cAAc,OAAO,gBAAgB,QAAQ;AAChD,cAAMC,WAAU,OAAO,OAAQ;AAC/B,cAAM,UAAU,mBAAoB,MAAyD,sKAAgL;AAC7Q,QAAAA,SAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAkDA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,YAAY,CAAC;AACnB,QAAM,eAAe,oBAAI,IAAI;AAC7B,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,UAAU;AACb,iBAAa,IAAI,KAAK;AACtB,QAAI,WAAW,QAAQ;AACrB,gBAAU,KAAK,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,8BAA8B,aAAa,IAAI,qBAAqB,wBAAwB;AAClG,MAAI,OAAO,cAAc,eAAe,aAAa,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,6BAA6B;AAE9I,UAAM,IAAI,MAAM,sKAAsK;AAAA,EACxL;AACA,SAAO,yBAAyB,CAAC,OAAO,cAAc,eAAe,YAAY,mCAAmC,IAAI,CAAC,GAAG,iBAAkB,GAAG,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,8BAA8B,CAAC,IAAI,sBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC1R;AAUA,IAAM,UAAU,IAAI,QAAQ,SAAS;", "names": ["plugin", "elem", "HydrationFeatureKind", "withI18nSupport", "withEventReplay", "withIncrementalHydration", "console"]}