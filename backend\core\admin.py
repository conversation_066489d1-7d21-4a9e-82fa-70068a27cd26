from django.contrib import admin
from .models import User, Company, Project, Post
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Role & Company', {'fields': ('role', 'company')}),
    )
    list_display = ('username', 'email', 'role', 'company', 'is_staff')

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name',)

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('name', 'company')

@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ('project', 'creator', 'scheduled_date', 'status')

# Register your models here.
