
<div class="p-6 max-w-3xl mx-auto" *ngIf="post">
  <h2 class="text-2xl font-bold mb-4">{{ post.title }}</h2>

  <p><strong>Status:</strong> {{ post.status }}</p>
  <p><strong>Scheduled Date:</strong> {{ post.scheduled_date | date: 'medium' }}</p>
  <p><strong>Description:</strong></p>
  <div class="border p-4 mb-4 whitespace-pre-wrap">{{ post.description }}</div>

  <!-- <div *ngIf="post.reviewComments?.length">
    <h3 class="text-lg font-semibold mb-2">Review Feedback</h3>
    <ul class="list-disc pl-5">
      <li *ngFor="let comment of post.reviewComments">{{ comment }}</li>
    </ul>
  </div> -->

  <button (click)="goBack()" class="mt-6 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
    Back to Posts
  </button>
</div>
