<h2 mat-dialog-title>Create Post for {{ data.date }}</h2>

<form #postForm="ngForm" (ngSubmit)="onSubmit(postForm)">
  <mat-form-field appearance="fill" class="w-100 mb-2">
    <mat-label>Title</mat-label>
    <input matInput name="title" [(ngModel)]="title" required />
  </mat-form-field>

  <mat-form-field appearance="fill" class="w-100 mb-2">
    <mat-label>Description</mat-label>
    <textarea matInput name="content" [(ngModel)]="content"></textarea>
  </mat-form-field>

  <input type="file" (change)="onFileChange($event)" accept="image/*,video/*" class="mb-2" />

  <div *ngIf="previewUrl">
    <img *ngIf="file && file.type && file.type.startsWith('image/')" [src]="previewUrl" width="100%" style="margin-bottom: 10px;" />

    <video *ngIf="file && file.type && file.type.startsWith('video/')" [src]="previewUrl" width="100%" controls></video>

  </div>

  <div class="mt-3 d-flex justify-content-end">
    <button mat-button type="button" (click)="onCancel()">Cancel</button>
    <button mat-raised-button color="primary" type="submit">Save</button>
  </div>
</form>
