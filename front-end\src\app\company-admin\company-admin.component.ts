import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';

import { CompanyAdminService } from './company-admin.service';
import { DashboardStats, ContentPost, Creator } from './models/dashboard.models';

@Component({
  selector: 'app-company-admin',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatGridListModule,
    MatListModule,
    MatDividerModule,
    MatBadgeModule
  ],
  templateUrl: './company-admin.component.html',
  styleUrl: './company-admin.component.css'
})
export class CompanyAdminComponent implements OnInit {
  dashboardStats: DashboardStats | null = null;
  recentContent: ContentPost[] = [];
  pendingReviews: ContentPost[] = [];
  creators: Creator[] = [];

  isLoading = false;
  currentDate = new Date();

  // Table columns
  pendingReviewsColumns: string[] = ['title', 'creator', 'project', 'scheduled_date', 'actions'];
  recentContentColumns: string[] = ['title', 'creator', 'status', 'scheduled_date'];
  creatorsColumns: string[] = ['name', 'projects', 'total_posts', 'pending_posts', 'status'];

  constructor(
    private router: Router,
    private companyAdminService: CompanyAdminService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    // Load all dashboard data
    this.companyAdminService.getDashboardStats().subscribe({
      next: (stats) => {
        this.dashboardStats = stats;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.showError('Failed to load dashboard statistics');
        this.isLoading = false;
      }
    });

    this.companyAdminService.getRecentContent().subscribe({
      next: (content) => {
        this.recentContent = content;
      },
      error: (error) => {
        console.error('Error loading recent content:', error);
      }
    });

    this.companyAdminService.getPendingReviews().subscribe({
      next: (reviews) => {
        this.pendingReviews = reviews;
      },
      error: (error) => {
        console.error('Error loading pending reviews:', error);
      }
    });

    this.companyAdminService.getCreators().subscribe({
      next: (creators) => {
        this.creators = creators;
      },
      error: (error) => {
        console.error('Error loading creators:', error);
      }
    });
  }

  // Content Actions
  approveContent(postId: number) {
    this.companyAdminService.approveContent(postId).subscribe({
      next: (response) => {
        this.showSuccess(response.message);
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error approving content:', error);
        this.showError('Failed to approve content');
      }
    });
  }

  rejectContent(postId: number) {
    this.companyAdminService.rejectContent(postId).subscribe({
      next: (response) => {
        this.showSuccess(response.message);
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error rejecting content:', error);
        this.showError('Failed to reject content');
      }
    });
  }

  requestChanges(postId: number) {
    this.companyAdminService.requestChanges(postId).subscribe({
      next: (response) => {
        this.showSuccess(response.message);
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error requesting changes:', error);
        this.showError('Failed to request changes');
      }
    });
  }

  // Utility methods
  getStatusColor(status: string): string {
    return this.companyAdminService.getStatusColor(status);
  }

  getStatusIcon(status: string): string {
    return this.companyAdminService.getStatusIcon(status);
  }

  formatDate(dateString: string): string {
    return this.companyAdminService.formatDate(dateString);
  }

  getActiveCreatorsCount(): number {
    return this.creators.filter(c => c.status === 'active').length;
  }

  // Safe getters for dashboard stats
  getTotalProjects(): number {
    return this.dashboardStats?.projects?.total || 0;
  }

  getActiveProjects(): number {
    return this.dashboardStats?.projects?.active || 0;
  }

  getCompletedProjects(): number {
    return this.dashboardStats?.projects?.completed || 0;
  }

  getTotalPosts(): number {
    return this.dashboardStats?.content?.total_posts || 0;
  }

  getPendingReviews(): number {
    return this.dashboardStats?.content?.pending_reviews || 0;
  }

  getApprovedPosts(): number {
    return this.dashboardStats?.content?.approved || 0;
  }

  getRejectedPosts(): number {
    return this.dashboardStats?.content?.rejected || 0;
  }

  getTotalCreators(): number {
    return this.dashboardStats?.creators?.total || 0;
  }

  showSuccess(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  showError(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  logout() {
    localStorage.clear();
    this.router.navigate(['/login']);
  }
}
